// FieldListComponent styles
.field-list-container {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;

  // Header
  .field-list-header {
    padding: 0.5rem 0.75rem;
    background-color: var(--bs-light);
    border-radius: 6px;
    border: 1px solid var(--bs-border-color);

    .field-count {
      font-size: 0.75rem;
      font-weight: 500;
      color: var(--bs-text-muted);
      text-transform: uppercase;
      letter-spacing: 0.5px;
    }
  }

  // Field list with 2-column grid
  .field-list {
    min-height: 2rem;
    padding: 1rem;


    // Dynamic Grid Layout (Single or Double Column)
    .field-grid {
      display: flex;
      flex-wrap: wrap;
      min-height: 100px;
      position: relative;
      transition: all 0.3s ease;

      // Default: Double column layout
      &.double-column {
        .field-grid-item {
          width: 48%;
          margin: 10px 1%;
          position: relative;
        }
      }

      // Single column layout
      &.single-column {
        flex-direction: column;

        .field-grid-item {
          width: 100%;
          margin: 5px 0;
          position: relative;
        }
      }

      // Responsive: Single column on mobile regardless of setting
      @media (max-width: 768px) {
        flex-direction: column;

        .field-grid-item {
          width: 100% !important;
          margin: 5px 0 !important;
        }
      }

      .field-grid-item {
        // Angular CDK Drag item styling
        &[cdkDrag] {
          cursor: grab;

          &:active {
            cursor: grabbing;
          }
        }
      }

      // External drop zone (for sidebar drops)
      .external-drop-zone {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        border: 2px dashed transparent;
        border-radius: 8px;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        justify-content: center;
        opacity: 0;
        pointer-events: none;
        z-index: 10;
        width: 100%;
        height: 100%;

        &.active {
          opacity: 1;
          border-color: var(--bs-primary);
          background-color: rgba(var(--bs-primary-rgb), 0.1);
          pointer-events: auto;
        }

        .drop-indicator {
          display: flex;
          align-items: center;
          justify-content: center;
          gap: 0.5rem;
          color: var(--bs-primary);
          font-size: 0.875rem;
          font-weight: 500;
          text-transform: uppercase;
          letter-spacing: 0.5px;
          padding: 1rem;
          background-color: rgba(255, 255, 255, 0.83);
          border-radius: 6px;
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
          width: 100%;
          height: 100%;

          mat-icon {
            font-size: 1.25rem;
            width: 1.25rem;
            height: 1.25rem;
          }
        }
      }
    }

    // Field item (updated for grid layout)
    .field-item {
      display: flex;
      align-items: center;
      gap: 0.75rem;
      padding: 1rem;
      background-color: var(--bs-white);
      border: 1px solid var(--bs-border-color);
      border-radius: 8px;
      transition: all 0.2s ease;
      cursor: grab;
      min-height: 80px;
      width: 100%;

      // Hover effect
      &:hover {
        border-color: var(--bs-primary);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        transform: translateY(-1px);
      }

      &:active {
        cursor: grabbing;
      }

      // Drag handle
      .drag-handle {
        display: flex;
        align-items: center;
        color: var(--bs-text-muted);
        cursor: grab;

        &:active {
          cursor: grabbing;
        }

        mat-icon {
          font-size: 1.125rem;
          width: 1.125rem;
          height: 1.125rem;
        }
      }

      // Field info
      .field-info {
        flex: 1;
        display: flex;
        flex-direction: column;
        gap: 0.25rem;
        min-width: 0; // Allow text truncation

        .field-label {
          font-size: 0.875rem;
          font-weight: 500;
          color: var(--bs-dark);
          display: flex;
          align-items: center;
          gap: 0.25rem;
          line-height: 1.2;

          .required-indicator {
            color: var(--bs-danger);
            font-weight: bold;
          }
        }

        .field-type {
          font-size: 0.75rem;
          color: var(--bs-text-muted);
          text-transform: uppercase;
          letter-spacing: 0.5px;
        }
      }

      // Field actions
      .field-actions {
        display: flex;
        align-items: center;

        .action-btn {
          width: 2rem;
          height: 2rem;

          mat-icon {
            font-size: 1rem;
            width: 1rem;
            height: 1rem;
          }
        }
      }

      // Drag preview
      .drag-preview {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        padding: 0.5rem 0.75rem;
        background-color: var(--bs-primary);
        color: white;
        border-radius: 4px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);

        .preview-content {
          display: flex;
          align-items: center;
          gap: 0.5rem;
          font-size: 0.875rem;
          font-weight: 500;
        }
      }
    }

    // Angular CDK Drag states
    .cdk-drag-preview {
      opacity: 0.8;
      transform: rotate(2deg);
      box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
      z-index: 1000;
      border-radius: 8px;
    }

    .cdk-drag-placeholder {
      opacity: 0.4;
      border: 2px dashed var(--bs-primary);
      background-color: var(--bs-light);
    }

    .cdk-drag-animating {
      transition: transform 250ms cubic-bezier(0, 0, 0.2, 1);
    }
  }

  // Empty state
  .empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 2rem 1rem;
    text-align: center;
    color: var(--bs-text-muted);

    .empty-icon {
      font-size: 3rem;
      width: 3rem;
      height: 3rem;
      margin-bottom: 0.75rem;
      opacity: 0.5;
    }

    .empty-message {
      font-size: 0.875rem;
      margin: 0;
      line-height: 1.4;
    }
  }
}

// Menu styles
.delete-action {
  color: var(--bs-danger) !important;

  mat-icon {
    color: var(--bs-danger) !important;
  }
}

// Responsive design
@media (max-width: 768px) {
  .field-list-container {
    .field-item {
      padding: 0.625rem;
      gap: 0.5rem;

      .field-info {
        .field-label {
          font-size: 0.8125rem;
        }

        .field-type {
          font-size: 0.6875rem;
        }
      }

      .action-btn {
        width: 1.75rem;
        height: 1.75rem;
      }
    }

    .empty-state {
      padding: 1.5rem 0.75rem;

      .empty-icon {
        font-size: 2.5rem;
        width: 2.5rem;
        height: 2.5rem;
      }

      .empty-message {
        font-size: 0.8125rem;
      }
    }
  }
}


// Global drag state styling
:global(.field-dragging) {
  .field-list {
    border-color: var(--bs-primary);
    background-color: rgba(var(--bs-primary-rgb), 0.05);
  }

  .drop-zone {
    opacity: 0.7 !important;
  }
}

// Enhanced drop zone animations
@keyframes dropZonePulse {
  0% {
    transform: scale(1);
    opacity: 0.7;
  }
  50% {
    transform: scale(1.02);
    opacity: 1;
  }
  100% {
    transform: scale(1);
    opacity: 0.7;
  }
}

.drop-zone.active {
  animation: dropZonePulse 2s ease-in-out infinite;
}

// Empty state for grid layout
.field-grid:empty {
  grid-column: 1 / -1;
  min-height: 120px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2px dashed var(--bs-border-color);
  border-radius: 8px;
  color: var(--bs-text-muted);
  font-style: italic;
  position: relative;

  &::before {
    content: 'Kéo thả field types vào đây hoặc click nút thêm';
    text-align: center;
    padding: 1rem;
  }

  &:hover {
    border-color: var(--bs-primary);
    background-color: rgba(var(--bs-primary-rgb), 0.05);
    color: var(--bs-primary);
  }
}
