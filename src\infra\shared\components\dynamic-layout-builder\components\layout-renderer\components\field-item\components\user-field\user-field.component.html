@if (isVisible()) {
  <div class="user-field-container" [class.read-only]="isReadOnlyState()">
    
    <div class="field-label-container">
      <label class="field-label">
        <!-- Field icon trước label -->
        <mat-icon class="field-type-icon me-2" [style.color]="getFieldIconColor()">{{ getFieldIcon() }}</mat-icon>
        {{ config.field.label }}
        @if (isFieldRequired()) {
          <span class="required-asterisk">*</span>
        }
      </label>
      
      @if (isReadOnlyState()) {
        <mat-icon
          class="read-only-icon ms-2"
          [matTooltip]="'DYNAMIC_LAYOUT_RENDERER.FIELD_MESSAGES.READ_ONLY_TOOLTIP' | translate"
          matTooltipPosition="above">
          lock
        </mat-icon>
      }
    </div>

    <div class="field-value-container">
      
      @if (config.currentViewMode === 'view') {
        <div class="field-view-value">
          <div class="user-info">
            <span class="user-name">{{ placeholderValue() }}</span>
            <small class="user-email text-muted">user&#64;example.com</small>
          </div>
        </div>
      }
      
      @else {
        <mat-form-field appearance="outline" class="w-100">
          <mat-select
            [placeholder]="(config.field.placeholder || 'DYNAMIC_LAYOUT_RENDERER.FIELD_PLACEHOLDERS.USER') | translate"
            [formControl]="formControl()">

            @for (user of getUserOptions(); track user.value) {
              <mat-option [value]="user.value">
                <div class="user-option">
                  <span class="user-name">{{ user.label }}</span>
                </div>
              </mat-option>
            }
          </mat-select>
        </mat-form-field>
      }

    <!-- Field-Level Validation Errors -->
    @if (shouldShowValidationErrors()) {
      <div class="field-validation-errors" aria-live="polite">
        @for (error of getValidationErrors(); track error) {
          <div class="validation-error-item">
            <mat-icon class="error-icon">error</mat-icon>
            <span class="error-message">{{ error | translate }}</span>
          </div>
        }
      </div>
    }
    </div>

    @if (config.field.tooltip) {
      <div class="field-tooltip">
        <small class="text-muted">{{ config.field.tooltip }}</small>
      </div>
    }
  </div>
}
