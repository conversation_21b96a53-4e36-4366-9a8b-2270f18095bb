import { Component, Input, Output, EventEmitter, ChangeDetectionStrategy, signal } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule, FormControl, AbstractControl, ValidationErrors } from '@angular/forms';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatSelectModule } from '@angular/material/select';
import { MatChipsModule } from '@angular/material/chips';
import { MatIconModule } from '@angular/material/icon';
import { MatTooltipModule } from '@angular/material/tooltip';
import { TranslateModule } from '@ngx-translate/core';

import { AbstractFieldComponent } from '../base/abstract-field.component';
import { FieldComponentInterface } from '../base/field-component.interface';
import { FieldItemConfig } from '../../../../../../models/dynamic-layout-renderer.model';
import { FieldValue, PicklistFieldConstraints, SelectFieldTypes } from '@domain/entities/field.entity';
import { generatePlaceHolderData, getFieldIcon } from '../../../../../../utils/field.utils';

/**
 * Component chuyên biệt xử lý các select-based fields
 * 
 * Supported field types:
 * - picklist: Dropdown chọn một giá trị
 * - multi-picklist: Multi-select chọn nhiều giá trị
 * - select: Standard select dropdown
 * - radio: Radio button group (rendered as select for consistency)
 * 
 * Features:
 * - View mode: Hiển thị mock data với chips cho multi-select
 * - Form mode: Select controls với search functionality
 * - Multi-select với chips display
 * - Mock options cho demo
 * - Permission-based visibility và read-only state
 * - i18n support
 */
@Component({
  selector: 'app-select-field',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatFormFieldModule,
    MatSelectModule,
    MatChipsModule,
    MatIconModule,
    MatTooltipModule,
    TranslateModule
  ],
  templateUrl: './select-field.component.html',
  styleUrls: ['./select-field.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class SelectFieldComponent extends AbstractFieldComponent implements FieldComponentInterface {

  /**
   * Configuration object chứa tất cả dữ liệu cần thiết
   */
  @Input({ required: true }) config!: FieldItemConfig;


  // Component-specific state
  placeholderValues = signal<string[]>([]);
  fieldOptions = signal<Array<{value: string, label: string}>>([]);

  // ===== IMPLEMENT ABSTRACT METHODS =====

  /**
   * Validate rằng field type được hỗ trợ
   */
  protected validateFieldType(): void {
    const supportedTypes: SelectFieldTypes[] = ['picklist', 'multi-picklist', 'select', 'radio'];
    if (!supportedTypes.includes(this.config.field.type as SelectFieldTypes)) {
      console.warn(`SelectFieldComponent: Unsupported field type '${this.config.field.type}'`);
    }
  }

  /**
   * Generate placeholder value dựa trên field type
   */
  protected override generatePlaceholderValue(): void {
    if (this.isMultiSelect()) {
      // Generate array of placeholder values for multi-select
      const basePlaceHolder = generatePlaceHolderData(this.config.field.type, this.translateService);
      const placeHolderArray = [`${basePlaceHolder} 1`, `${basePlaceHolder} 2`];
      this.placeholderValues.set(placeHolderArray);
      this.placeholderValue.set(placeHolderArray.join(', '));
    } else {
      // Generate single placeholder value
      this.placeholderValue.set(generatePlaceHolderData(this.config.field.type, this.translateService));
    }
  }

  /**
   * Lấy validators phù hợp cho field type
   * UPDATED: Thêm custom validator để check value có tồn tại trong field options không
   */
  protected override getValidators(): any[] {
    const validators = this.getCommonValidators();

    // Thêm custom validator để check value có hợp lệ với field options không
    // Điều này đảm bảo validation chính xác cho select fields
    validators.push(this.createSelectFieldValidator());

    return validators;
  }

  /**
   * Tạo custom validator cho select field
   * Validator này check xem value có tồn tại trong field options không
   */
  private createSelectFieldValidator() {
    return (control: AbstractControl): ValidationErrors | null => {
      const value = control.value;

      // Nếu không có value, để required validator xử lý
      if (!value) {
        return null;
      }

      // Lấy field options hiện tại
      const options = this.fieldOptions();

      // Nếu chưa có options, tạm thời coi là valid (options sẽ được load sau)
      if (!options || options.length === 0) {
        return null;
      }

      // Check value có tồn tại trong options không
      if (this.isMultiSelect()) {
        // Multi-select: value phải là array và tất cả items phải có trong options
        if (!Array.isArray(value)) {
          return { invalidSelection: true };
        }

        const validValues = options.map(opt => opt.value);
        const hasInvalidValue = value.some(v => !validValues.includes(v));

        return hasInvalidValue ? { invalidSelection: true } : null;
      } else {
        // Single select: value phải có trong options
        const validValues = options.map(opt => opt.value);
        return validValues.includes(value) ? null : { invalidSelection: true };
      }
    };
  }


  // ===== IMPLEMENT INTERFACE METHODS =====

  /**
   * Handle khi field.value thay đổi từ bên ngoài
   */
  override handleFieldValueChange(): void {
    super.handleFieldValueChange();
    this.loadFieldOptions();
  }

  override initializeField(): void {
    super.initializeField();
    this.loadFieldOptions();
  }

  /**
   * Override initializeFormControl để đảm bảo field options được load trước khi FormControl được khởi tạo
   * Điều này fix vấn đề validation error hiển thị mặc dù field đã có initial value
   */
  override initializeFormControl(): void {
    // 1. Load field options trước tiên
    this.loadFieldOptions();

    // 2. Sau đó mới khởi tạo FormControl với validators
    super.initializeFormControl();
  }


  // ===== COMPONENT-SPECIFIC METHODS =====

  /**
   * Load field options từ field constraints
   */
  private loadFieldOptions(): void {
    const constraints = this.config.field.constraints as PicklistFieldConstraints;
    let options: Array<{value: string, label: string}> = [];

    // Đọc từ picklistOptions nếu có
    if (constraints?.picklistOptions && Array.isArray(constraints.picklistOptions)) {
      options = constraints.picklistOptions.map((option: any) => ({
        value: option.value.toString(),
        label: option.label
      }));
    }

    this.fieldOptions.set(options);
  }

  /**
   * Kiểm tra xem có phải multi-select không
   */
  isMultiSelect(): boolean {
    return this.config.field.type === 'multi-picklist';
  }

  /**
   * Lấy label của option dựa trên value
   */
  getOptionLabel(value: string): string {
    const option = this.fieldOptions().find(opt => opt.value === value);
    return option?.label || value || '';
  }

  /**
   * Xử lý khi selection thay đổi
   */
  onSelectionChange(event: any): void {
    const fieldId = this.config.field._id || '';
    const value = event.value;

    // Emit value change event
    this.valueChange.emit({ fieldId, value });
  }

  /**
   * Remove chip trong multi-select mode
   */
  removeChip(chipValue: string): void {
    if (this.isReadOnly()) return;

    const currentValue = this.formControl().value || [];
    const newValue = currentValue.filter((value: string) => value !== chipValue);
    this.formControl().setValue(newValue);
  }
}
