/**
 * Styles cho LayoutSelectorComponent
 * Component cho vi<PERSON><PERSON> chọn và quản lý layouts trong Dynamic Layout Builder
 */

.layout-selector-container {
  display: flex;
  align-items: end;
}

.layout-selector-wrapper {
  flex: 1;
  min-width: 300px;
}

.form-label {
  font-weight: 600;
  color: #495057;
  margin-bottom: 0.5rem;
  font-size: 0.875rem;
}

// Layout Menu Container
.layout-menu-container {
  position: relative;
  width: 100%;
}

// Menu Trigger Button
.layout-trigger-button {
  width: 100%;
  display: flex !important;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem 1rem;
  font-size: 0.875rem;
  border: 1px solid #ced4da;
  border-radius: 6px;
  background: #ffffff;
  color: #495057;
  text-align: left;
  transition: all 0.2s ease;
  min-height: 38px;

  &:hover {
    border-color: #80bdff;
    background: #f8f9fa;
  }

  &:focus {
    border-color: #80bdff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
  }

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    background: #e9ecef;
  }

  .layout-name {
    flex: 1;
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
  }

  .default-badge {
    font-size: 0.75rem;
    color: #6c757d;
    font-style: italic;
  }

  .loading-icon {
    font-size: 1rem;
    color: #007bff;
  }

  mat-icon {
    font-size: 1.2rem;
    width: 1.2rem;
    height: 1.2rem;
    color: #6c757d;
  }
}

// Layout Menu Styles
::ng-deep .layout-menu {
  .mat-mdc-menu-item {
    font-size: 0.875rem;
    line-height: 1.5;
    padding: 0.5rem 1rem;
    min-height: 40px;

    &.selected {
      background-color: #e3f2fd;
      color: #1976d2;
      font-weight: 500;
    }

    &:hover {
      background-color: #f5f5f5;
    }

    mat-icon {
      margin-right: 0.75rem;
      font-size: 1.1rem;
      width: 1.1rem;
      height: 1.1rem;
    }

    .default-badge {
      font-size: 0.75rem;
      color: #6c757d;
      font-style: italic;
      margin-left: 0.5rem;
    }

    &.create-new-item {
      border-top: 1px solid #e9ecef;
      margin-top: 0.25rem;
      color: #007bff;
      font-weight: 500;

      &:hover {
        background-color: #e3f2fd;
      }

      .create-icon {
        color: #007bff;
      }
    }
  }

  .mat-mdc-menu-content {
    padding: 0.25rem 0;
  }
}

.layout-info {
  width: 100px;
  overflow: hidden;
  font-size: .8em;

  // padding: 0.5rem 0.75rem;
  // background: #ffffff;
  // border: 1px solid #e9ecef;
  // border-radius: 4px;
  // border-top: none;
  // border-top-left-radius: 0;
  // border-top-right-radius: 0;

  .info-icon,
  .clock-icon {
    font-size: 0.875rem;
    width: 0.875rem;
    height: 0.875rem;
    margin-right: 0.25rem;
    vertical-align: middle;
  }
}

// Removed create-layout-wrapper and btn-outline-primary styles
// as the create button is now part of the menu

.input-group-text {
  background: #ffffff;
  border-left: none;
}

.fa-spinner {
  color: #007bff;
}

// Responsive styles
@media (max-width: 768px) {
  .layout-selector-container {
    flex-direction: column;
    align-items: stretch;
    gap: 0.75rem;
    padding: 0.75rem;
  }

  .layout-selector-wrapper {
    min-width: auto;
  }

  .form-label {
    font-size: 0.8rem;
  }

  .layout-trigger-button {
    font-size: 0.8rem;
    padding: 0.625rem 0.875rem;
  }

  .layout-info {
    font-size: 0.75rem;
  }

  // Menu responsive styles
  ::ng-deep .layout-menu {
    .mat-mdc-menu-item {
      font-size: 0.8rem;
      padding: 0.375rem 0.875rem;
      min-height: 36px;
    }
  }
}

@media (max-width: 576px) {
  .layout-selector-container {
    margin-bottom: 1rem;
    padding: 0.5rem;
  }

  .layout-info {
    padding: 0.375rem 0.5rem;
  }

  .layout-trigger-button {
    padding: 0.5rem 0.75rem;
    font-size: 0.75rem;
  }

  // Mobile menu styles
  ::ng-deep .layout-menu {
    .mat-mdc-menu-item {
      font-size: 0.75rem;
      padding: 0.25rem 0.75rem;
      min-height: 32px;
    }
  }
}

// Dark mode support (nếu cần)
@media (prefers-color-scheme: dark) {
  .layout-selector-container {
    background: #2d3748;
    border-color: #4a5568;
  }

  .form-label {
    color: #e2e8f0;
  }

  .layout-trigger-button {
    background: #4a5568;
    border-color: #718096;
    color: #e2e8f0;

    &:hover {
      background: #2d3748;
      border-color: #80bdff;
    }

    &:disabled {
      background: #2d3748;
      color: #718096;
    }
  }

  .layout-info {
    background: #4a5568;
    border-color: #718096;
    color: #cbd5e0;
  }

  // Dark mode menu styles
  ::ng-deep .layout-menu {
    .mat-mdc-menu-panel {
      background: #4a5568;
    }

    .mat-mdc-menu-item {
      color: #e2e8f0;

      &:hover {
        background-color: #2d3748;
      }

      &.selected {
        background-color: #1a365d;
        color: #63b3ed;
      }

      &.create-new-item {
        color: #63b3ed;
        border-top-color: #718096;

        &:hover {
          background-color: #1a365d;
        }
      }
    }
  }
}
