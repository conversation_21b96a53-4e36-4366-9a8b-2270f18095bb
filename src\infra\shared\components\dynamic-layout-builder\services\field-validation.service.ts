import { Injectable } from '@angular/core';
import { ValidatorFn, Validators } from '@angular/forms';
import { 
  Field, 
  FieldValue, 
  TextFieldConstraints, 
  NumberFieldConstraints, 
  DecimalFieldConstraints, 
  CurrencyFieldConstraints, 
  PicklistFieldConstraints, 
  FileFieldConstraints,
  CommonFieldConstraints,
  FieldConstraints
} from '@domain/entities/field.entity';

/**
 * Interface cho kết quả validation
 */
export interface ValidationResult {
  isValid: boolean;
  errors: string[];
}

/**
 * FieldValidationService - Tập trung hóa tất cả validation logic
 * 
 * <PERSON>ân thủ Clean Architecture:
 * - Nằm trong Infrastructure layer
 * - Sử dụng Domain entities (Field, FieldConstraints)
 * - Cung cấp centralized validation cho tất cả components
 * 
 * Lợi ích:
 * - Consistent validation behavior
 * - Dễ maintain và extend
 * - Type safety với Field constraints
 * - Tránh duplicate code
 */
@Injectable({
  providedIn: 'root'
})
export class FieldValidationService {

  /**
   * Lấy tất cả validators cho một field
   * @param field - Field object chứa type và constraints
   * @returns Array of ValidatorFn
   */
  getValidatorsForField(field: Field): ValidatorFn[] {
    const validators: ValidatorFn[] = [];
    
    // Common validators (required, etc.)
    validators.push(...this.getCommonValidators(field.constraints as FieldConstraints));
    
    // Field type specific validators
    validators.push(...this.getFieldTypeValidators(field));
    
    return validators;
  }

  /**
   * Validate giá trị của field
   * @param field - Field object
   * @param value - Giá trị cần validate
   * @returns ValidationResult
   */
  validateFieldValue(field: Field, value: FieldValue): ValidationResult {
    const errors: string[] = [];

    // Check required constraint
    const isRequired = this.isFieldRequired(field);
    if (isRequired && this.isEmpty(value)) {
      errors.push('FORM_VALIDATION.FIELD_REQUIRED');
    }

    // Skip other validations if value is empty and not required
    if (this.isEmpty(value) && !isRequired) {
      return { isValid: true, errors: [] };
    }

    // Type-specific validations
    this.validateByFieldType(field, value, errors);

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * Lấy common validators (required, readonly, disabled)
   * @param constraints - Field constraints
   * @returns Array of ValidatorFn
   */
  getCommonValidators(constraints?: FieldConstraints): ValidatorFn[] {
    const validators: ValidatorFn[] = [];

    if (!constraints) return validators;

    // Required validator
    if (this.getConstraintValue(constraints, 'isRequired')) {
      validators.push(Validators.required);
    }

    return validators;
  }

  /**
   * Lấy validators theo từng loại field
   * @param field - Field object
   * @returns Array of ValidatorFn
   */
  getFieldTypeValidators(field: Field): ValidatorFn[] {
    const validators: ValidatorFn[] = [];

    switch (field.type) {
      case 'text':
      case 'email':
      case 'phone':
      case 'url':
      case 'search':
        validators.push(...this.getTextFieldValidators(field));
        break;
      case 'textarea':
        validators.push(...this.getTextareaFieldValidators(field));
        break;
      case 'number':
      case 'decimal':
      case 'currency':
      case 'percent':
        validators.push(...this.getNumberFieldValidators(field));
        break;
      case 'picklist':
        validators.push(...this.getPicklistFieldValidators(field));
        break;
      case 'multi-picklist':
        validators.push(...this.getMultiPicklistFieldValidators(field));
        break;
      case 'file':
      case 'image':
        validators.push(...this.getFileFieldValidators(field));
        break;
      case 'date':
      case 'datetime':
        validators.push(...this.getDateFieldValidators(field));
        break;
      case 'checkbox':
        validators.push(...this.getCheckboxFieldValidators(field));
        break;
      case 'user':
        validators.push(...this.getUserFieldValidators(field));
        break;
    }

    return validators;
  }

  // ===== PRIVATE METHODS - FIELD TYPE SPECIFIC VALIDATORS =====

  /**
   * Text field validators (text, email, phone, url, search)
   */
  private getTextFieldValidators(field: Field): ValidatorFn[] {
    const validators: ValidatorFn[] = [];
    const constraints = field.constraints as TextFieldConstraints;

    if (!constraints) return validators;

    // Length validators
    if (constraints.minLength) {
      validators.push(Validators.minLength(constraints.minLength));
    }
    if (constraints.maxLength) {
      validators.push(Validators.maxLength(constraints.maxLength));
    }

    // Pattern validator
    if (constraints.pattern) {
      validators.push(Validators.pattern(constraints.pattern));
    }

    // Type-specific validators
    switch (field.type) {
      case 'email':
        validators.push(Validators.email);
        break;
      case 'url':
        validators.push(Validators.pattern(/^https?:\/\/.+/));
        break;
      case 'phone':
        validators.push(Validators.pattern(/^[\d\s\-\+\(\)]+$/));
        break;
    }

    return validators;
  }

  /**
   * Textarea field validators
   */
  private getTextareaFieldValidators(field: Field): ValidatorFn[] {
    const validators: ValidatorFn[] = [];
    const constraints = field.constraints as TextFieldConstraints;

    if (!constraints) return validators;

    if (constraints.maxLength) {
      validators.push(Validators.maxLength(constraints.maxLength));
    }

    return validators;
  }

  /**
   * Number field validators (number, decimal, currency, percent)
   */
  private getNumberFieldValidators(field: Field): ValidatorFn[] {
    const validators: ValidatorFn[] = [];
    const constraints = field.constraints as NumberFieldConstraints;

    if (!constraints) return validators;

    if (constraints.min !== undefined) {
      validators.push(Validators.min(constraints.min));
    }
    if (constraints.max !== undefined) {
      validators.push(Validators.max(constraints.max));
    }

    return validators;
  }

  /**
   * Picklist field validators
   */
  private getPicklistFieldValidators(field: Field): ValidatorFn[] {
    const validators: ValidatorFn[] = [];
    // Picklist validation logic có thể được thêm ở đây
    return validators;
  }

  /**
   * Multi-picklist field validators
   */
  private getMultiPicklistFieldValidators(field: Field): ValidatorFn[] {
    const validators: ValidatorFn[] = [];
    // Multi-picklist validation logic có thể được thêm ở đây
    return validators;
  }

  /**
   * File field validators
   */
  private getFileFieldValidators(field: Field): ValidatorFn[] {
    const validators: ValidatorFn[] = [];
    // File validation logic có thể được thêm ở đây
    return validators;
  }

  /**
   * Date field validators
   */
  private getDateFieldValidators(field: Field): ValidatorFn[] {
    const validators: ValidatorFn[] = [];
    // Date validation logic có thể được thêm ở đây
    return validators;
  }

  /**
   * Checkbox field validators
   */
  private getCheckboxFieldValidators(field: Field): ValidatorFn[] {
    const validators: ValidatorFn[] = [];
    // Checkbox validation logic có thể được thêm ở đây
    return validators;
  }

  /**
   * User field validators
   */
  private getUserFieldValidators(field: Field): ValidatorFn[] {
    const validators: ValidatorFn[] = [];
    // User field validation logic có thể được thêm ở đây
    return validators;
  }

  // ===== PRIVATE METHODS - TYPE-SPECIFIC VALIDATION =====

  /**
   * Validate theo field type (cho validateFieldValue method)
   */
  private validateByFieldType(field: Field, value: FieldValue, errors: string[]): void {
    switch (field.type) {
      case 'text':
      case 'email':
      case 'phone':
      case 'url':
        this.validateTextField(field, value as string, errors);
        break;
      case 'textarea':
        this.validateTextareaField(field, value as string, errors);
        break;
      case 'number':
      case 'decimal':
      case 'currency':
      case 'percent':
        this.validateNumberField(field, value as number, errors);
        break;
      case 'picklist':
        this.validatePicklistField(field, value as string, errors);
        break;
      case 'multi-picklist':
        this.validateMultiPicklistField(field, value as string[], errors);
        break;
      case 'file':
      case 'image':
        this.validateFileField(field, value, errors);
        break;
    }
  }

  /**
   * Validate text field (text, email, phone, url)
   */
  private validateTextField(field: Field, value: string, errors: string[]): void {
    if (!value) return;

    const constraints = field.constraints as TextFieldConstraints;
    if (!constraints) return;

    // Max length validation
    if (constraints.maxLength && value.length > constraints.maxLength) {
      errors.push('FORM_VALIDATION.TEXT_TOO_LONG');
    }

    // Min length validation
    if (constraints.minLength && value.length < constraints.minLength) {
      errors.push('FORM_VALIDATION.TEXT_TOO_SHORT');
    }

    // Email format validation
    if (field.type === 'email' && !this.isValidEmail(value)) {
      errors.push('FORM_VALIDATION.INVALID_EMAIL');
    }

    // Phone format validation
    if (field.type === 'phone' && constraints.maxDigits) {
      const digits = value.replace(/\D/g, '');
      if (digits.length > constraints.maxDigits) {
        errors.push('FORM_VALIDATION.PHONE_TOO_LONG');
      }
    }

    // URL format validation
    if (field.type === 'url' && !this.isValidUrl(value)) {
      errors.push('FORM_VALIDATION.INVALID_URL');
    }

    // Pattern validation
    if (constraints.pattern) {
      const regex = new RegExp(constraints.pattern);
      if (!regex.test(value)) {
        errors.push('FORM_VALIDATION.INVALID_PATTERN');
      }
    }
  }

  /**
   * Validate textarea field
   */
  private validateTextareaField(field: Field, value: string, errors: string[]): void {
    if (!value) return;

    const constraints = field.constraints as TextFieldConstraints;
    if (!constraints) return;

    if (constraints.maxLength && value.length > constraints.maxLength) {
      errors.push('FORM_VALIDATION.TEXT_TOO_LONG');
    }

    if (constraints.minLength && value.length < constraints.minLength) {
      errors.push('FORM_VALIDATION.TEXT_TOO_SHORT');
    }
  }

  /**
   * Validate number field (number, decimal, currency, percent)
   */
  private validateNumberField(field: Field, value: number, errors: string[]): void {
    if (value === null || value === undefined) return;

    const constraints = field.constraints as NumberFieldConstraints;
    if (!constraints) return;

    // Min/Max validation
    if (constraints.min !== undefined && value < constraints.min) {
      errors.push('FORM_VALIDATION.NUMBER_TOO_SMALL');
    }
    if (constraints.max !== undefined && value > constraints.max) {
      errors.push('FORM_VALIDATION.NUMBER_TOO_LARGE');
    }

    // Max digits validation
    if (constraints.maxDigits) {
      const digits = Math.abs(value).toString().replace('.', '').length;
      if (digits > constraints.maxDigits) {
        errors.push('FORM_VALIDATION.NUMBER_TOO_LONG');
      }
    }

    // Decimal places validation for currency/decimal fields
    if (field.type === 'currency' || field.type === 'decimal') {
      const decimalPlaces = (constraints as any).decimalPlaces;
      if (decimalPlaces !== undefined) {
        const decimalPart = value.toString().split('.')[1];
        if (decimalPart && decimalPart.length > decimalPlaces) {
          errors.push('FORM_VALIDATION.TOO_MANY_DECIMAL_PLACES');
        }
      }
    }
  }

  /**
   * Validate picklist field
   */
  private validatePicklistField(field: Field, value: string, errors: string[]): void {
    if (!value) return;

    const constraints = field.constraints as PicklistFieldConstraints;
    if (!constraints || !constraints.picklistOptions) return;

    // Check if value exists in picklist options
    const validOptions = constraints.picklistOptions.map(option => option.value);
    if (!validOptions.includes(value)) {
      errors.push('FORM_VALIDATION.INVALID_PICKLIST_VALUE');
    }
  }

  /**
   * Validate multi-picklist field
   */
  private validateMultiPicklistField(field: Field, value: string[], errors: string[]): void {
    if (!value || !Array.isArray(value)) return;

    const constraints = field.constraints as PicklistFieldConstraints;
    if (!constraints || !constraints.picklistOptions) return;

    // Check if all values exist in picklist options
    const validOptions = constraints.picklistOptions.map(option => option.value);
    const invalidValues = value.filter(val => !validOptions.includes(val));

    if (invalidValues.length > 0) {
      errors.push('FORM_VALIDATION.INVALID_PICKLIST_VALUE');
    }
  }

  /**
   * Validate file field
   */
  private validateFileField(field: Field, value: FieldValue, errors: string[]): void {
    // File validation logic - có thể được implement sau
    // Placeholder for now
  }

  // ===== HELPER METHODS =====

  /**
   * Kiểm tra xem field có required không từ constraints
   */
  private isFieldRequired(field: Field): boolean {
    return this.getConstraintValue(field.constraints, 'isRequired') || false;
  }

  /**
   * Lấy giá trị constraint một cách type-safe
   */
  private getConstraintValue(constraints: any, key: string): any {
    return constraints && key in constraints ? constraints[key] : undefined;
  }

  /**
   * Kiểm tra giá trị có trống không
   */
  private isEmpty(value: FieldValue): boolean {
    return value === null ||
           value === undefined ||
           value === '' ||
           (Array.isArray(value) && value.length === 0);
  }

  /**
   * Validate email format
   */
  private isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  /**
   * Validate URL format
   */
  private isValidUrl(url: string): boolean {
    try {
      new URL(url);
      return true;
    } catch {
      return false;
    }
  }

  /**
   * Kiểm tra xem field có public không từ constraints
   */
  isFieldPublic(field: Field): boolean {
    return this.getConstraintValue(field.constraints, 'isPublic') || false;
  }

  /**
   * Kiểm tra xem field có readonly không từ constraints
   */
  isFieldReadonly(field: Field): boolean {
    return this.getConstraintValue(field.constraints, 'isReadonly') || false;
  }

  /**
   * Kiểm tra xem field có disabled không từ constraints
   */
  isFieldDisabled(field: Field): boolean {
    return this.getConstraintValue(field.constraints, 'isDisabled') || false;
  }
}
