// Comprehensive mock data for Dynamic Layout Builder testing
import { DynamicLayoutConfig } from "@/shared/components/dynamic-layout-builder/models/dynamic-layout-config.model";
import { DynamicLayoutRendererConfig } from "@/shared/components/dynamic-layout-builder/models/dynamic-layout-renderer.model";
import { FieldPermissionProfile, FieldOption } from "@domain/entities/field.entity";

/**
 * Helper function để tạo FieldOption array từ string array
 * @param values Array các string values
 * @returns Array các FieldOption không có _id
 */
function createFieldOptions(values: string[]): FieldOption[] {
  return values.map((value, index) => ({
    label: value,
    value: value
  }));
}

// Mock data chung cho tất cả layouts
const mockDefaultProfiles: FieldPermissionProfile[] = [
  { _id: 'admin', name: 'Administrator', permission: 'read_write' },
  { _id: 'manager', name: 'Manager', permission: 'read_write' },
  { _id: 'user', name: 'Standard User', permission: 'read' },
  { _id: 'guest', name: 'Guest User', permission: 'none' }
];

const mockFieldSettings = {
  availableSearchModules: [
    { _id: 'sales_quotes', name: 'Sales Quotes' },
    { _id: 'contacts', name: 'Contacts' },
    { _id: 'transactions', name: 'Transactions' }
  ]
};

// Mock layouts với đầy đủ dữ liệu theo yêu cầu
export const simpleMockLayouts: DynamicLayoutConfig[] = [
  {
    _id: 'fashion-layout',
    title: 'Layout Thời Trang',
    name: 'Layout Thời Trang',
    shortDescription: 'Layout chuyên dụng cho ngành thời trang với các trường về size, màu sắc, phong cách',
    isDefault: true,
    createdAt: new Date('2024-01-01T00:00:00Z'),
    updatedAt: new Date('2024-01-15T10:30:00Z'),
    fieldDefaultSettings: {
      permissionProfiles: mockDefaultProfiles,
      availableSearchModules: mockFieldSettings.availableSearchModules
    },
    sections: [
      {
        _id: 'personal-info',
        title: 'Thông tin cá nhân',
        fields: [
          { _id: 'field-xxxx-test', type: 'picklist', label: 'Size x', order: 4, permissionProfiles: [], constraints: { picklistOptions: createFieldOptions(['S', 'M', 'L', 'XL', 'XXL']), sortOrder: 'input', isRequired: true } },
          { _id: 'field-1', type: 'text', label: 'Họ và tên', order: 1, permissionProfiles: [], constraints: { isRequired: true } },
          { _id: 'field-2', type: 'phone', label: 'Số điện thoại', order: 2, permissionProfiles: [], constraints: { isRequired: true } },
          { _id: 'field-3', type: 'email', label: 'Email', order: 3, permissionProfiles: [], constraints: { isRequired: false } },
          { _id: 'field-4', type: 'date', label: 'Ngày sinh', order: 4, permissionProfiles: [], constraints: { isRequired: false } },
          { _id: 'field-5', type: 'text', label: 'Địa chỉ', order: 5, permissionProfiles: [], constraints: { isRequired: false } }
        ],
        settings: {
          sectionLayoutColumn: 'double-column' as 'double-column'
        }
      },
      {
        _id: 'fashion-preferences',
        title: 'Sở thích thời trang',
        fields: [
          { _id: 'field-6', type: 'multi-picklist', label: 'Size thường mặc', order: 1, permissionProfiles: [], defaultValue: ['M'], constraints: { picklistOptions: createFieldOptions(['S', 'M', 'L', 'XL', 'XXL']), sortOrder: 'input', isRequired: true } },
          { _id: 'field-7', type: 'picklist', label: 'Màu sắc yêu thích', order: 2, permissionProfiles: [], constraints: { picklistOptions: createFieldOptions(['Đỏ', 'Xanh', 'Vàng', 'Đen', 'Trắng']), sortOrder: 'input', isRequired: false } },
          { _id: 'field-8', type: 'picklist', label: 'Phong cách', order: 3, permissionProfiles: [], constraints: { picklistOptions: createFieldOptions(['Casual', 'Formal', 'Sport', 'Vintage']), sortOrder: 'input', isRequired: false } },
          { _id: 'field-9', type: 'picklist', label: 'Thương hiệu yêu thích', order: 4, permissionProfiles: [], constraints: { picklistOptions: createFieldOptions(['Nike', 'Adidas', 'Zara', 'H&M', 'Uniqlo']), sortOrder: 'input', isRequired: false } },
          { _id: 'field-10', type: 'textarea', label: 'Ghi chú đặc biệt', order: 5, permissionProfiles: [], constraints: { textType: 'large', isRequired: false } }
        ],
        settings: {
          sectionLayoutColumn: 'double-column' as 'double-column'
        }
      },
      {
        _id: 'purchase-history',
        title: 'Lịch sử mua hàng',
        fields: [
          { _id: 'field-11', type: 'number', label: 'Số đơn hàng', order: 1, permissionProfiles: [], constraints: { isRequired: false } },
          { _id: 'field-12', type: 'number', label: 'Tổng chi tiêu', order: 2, permissionProfiles: [], constraints: { isRequired: false } },
          { _id: 'field-13', type: 'date', label: 'Lần mua cuối', order: 3, permissionProfiles: [], constraints: { isRequired: false } },
          { _id: 'field-14', type: 'picklist', label: 'Loại sản phẩm thường mua', order: 4, permissionProfiles: [], constraints: { picklistOptions: createFieldOptions(['Quần áo', 'Giày dép', 'Phụ kiện', 'Túi xách']), sortOrder: 'input', isRequired: false } },
          { _id: 'field-15', type: 'checkbox', label: 'Đăng ký nhận thông báo', order: 5, permissionProfiles: [], constraints: { isRequired: false } },
          { _id: 'field-16', type: 'file', label: 'Hóa đơn mẫu', order: 6, permissionProfiles: [], constraints: { isRequired: false } },
          { _id: 'field-17', type: 'user', label: 'Nhân viên phụ trách', order: 7, permissionProfiles: [], constraints: { userType: 'single', isRequired: true } }
        ],
        settings: {
          sectionLayoutColumn: 'single-column' as 'single-column'
        }
      }
    ],
    showPreviewPanel: true,
    enablePreviewMode: true,
    showToolbarActions: true,
    autoSave: false,
    autoSaveInterval: 30000,
    enableDragDrop: true,
    enableInlineEdit: true,
    enableQuickCreate: true,
    quickCreateConfig: {
      title: 'Tạo nhanh khách hàng thời trang',
      description: 'Form tạo nhanh thông tin khách hàng thời trang',
      section: {
        _id: 'qc-fashion-section',
        title: 'Thông tin cơ bản',
        fields: [
          // Copy từ personal-info section với _id khớp nhau
          { _id: 'field-1', type: 'text', label: 'Họ và tên', order: 1, permissionProfiles: [], constraints: { isRequired: true } },
          { _id: 'field-2', type: 'phone', label: 'Số điện thoại', order: 2, permissionProfiles: [], constraints: { isRequired: true } },
          { _id: 'field-3', type: 'email', label: 'Email', order: 3, permissionProfiles: [], constraints: { isRequired: false } },
          // Copy từ fashion-preferences section với _id khớp nhau
          { _id: 'field-6', type: 'picklist', label: 'Size thường mặc', order: 4, permissionProfiles: [], constraints: { picklistOptions: createFieldOptions(['S', 'M', 'L', 'XL', 'XXL']), sortOrder: 'input', isRequired: true } },
          { _id: 'field-8', type: 'picklist', label: 'Phong cách', order: 5, permissionProfiles: [], constraints: { picklistOptions: createFieldOptions(['Casual', 'Formal', 'Sport', 'Vintage']), sortOrder: 'input', isRequired: false } }
        ],
        settings: {
          sectionLayoutColumn: 'double-column' as 'double-column'
        }
      },
      enableAutoSave: false
    },
    detailViewConfig: {
      title: 'Chi tiết khách hàng thời trang',
      description: 'Hiển thị thông tin chi tiết khách hàng trong ngành thời trang',
      sections: [
        {
          _id: 'dv-basic-info',
          name: 'Thông tin cơ bản',
          widgets: [
            { _id: 'widget-1', name: 'Thông tin cá nhân', description: 'Hiển thị họ tên, SĐT, email' },
            { _id: 'widget-2', name: 'Địa chỉ liên hệ', description: 'Hiển thị địa chỉ và thông tin liên hệ' },
            { _id: 'widget-3', name: 'Thông tin sinh nhật', description: 'Hiển thị ngày sinh và tuổi' }
          ]
        },
        {
          _id: 'dv-fashion-info',
          name: 'Thông tin thời trang',
          widgets: [
            { _id: 'widget-4', name: 'Sở thích thời trang', description: 'Size, màu sắc, phong cách yêu thích' },
            { _id: 'widget-5', name: 'Thương hiệu yêu thích', description: 'Danh sách thương hiệu ưa chuộng' },
            { _id: 'widget-6', name: 'Ghi chú đặc biệt', description: 'Các yêu cầu và ghi chú riêng' }
          ]
        },
        {
          _id: 'dv-purchase-history',
          name: 'Lịch sử mua hàng',
          widgets: [
            { _id: 'widget-7', name: 'Thống kê đơn hàng', description: 'Số lượng và tổng giá trị đơn hàng' },
            { _id: 'widget-8', name: 'Sản phẩm đã mua', description: 'Danh sách sản phẩm đã mua' },
            { _id: 'widget-9', name: 'Lần mua cuối', description: 'Thông tin đơn hàng gần nhất' }
          ]
        },
        {
          _id: 'dv-loyalty',
          name: 'Chương trình khách hàng thân thiết',
          widgets: [
            { _id: 'widget-10', name: 'Điểm tích lũy', description: 'Số điểm hiện tại và lịch sử tích lũy' },
            { _id: 'widget-11', name: 'Hạng thành viên', description: 'Cấp độ thành viên và quyền lợi' },
            { _id: 'widget-12', name: 'Ưu đãi cá nhân', description: 'Các chương trình ưu đãi riêng' }
          ]
        },
        {
          _id: 'dv-communication',
          name: 'Tương tác và liên lạc',
          widgets: [
            { _id: 'widget-13', name: 'Lịch sử tương tác', description: 'Các cuộc gọi, email, tin nhắn' },
            { _id: 'widget-14', name: 'Phản hồi và đánh giá', description: 'Đánh giá sản phẩm và dịch vụ' },
            { _id: 'widget-15', name: 'Cài đặt thông báo', description: 'Tùy chọn nhận thông báo' }
          ]
        }
      ]
    }
  },

  // Layout 2: Mỹ phẩm
  {
    _id: 'cosmetics-layout',
    title: 'Layout Mỹ Phẩm',
    name: 'Layout Mỹ Phẩm',
    shortDescription: 'Layout chuyên dụng cho ngành mỹ phẩm với các trường về loại da, vấn đề da',
    isDefault: false,
    createdAt: new Date('2024-01-02T00:00:00Z'),
    updatedAt: new Date('2024-01-16T14:20:00Z'),
    fieldDefaultSettings: {
      permissionProfiles: mockDefaultProfiles,
      availableSearchModules: mockFieldSettings.availableSearchModules
    },
    sections: [
      {
        _id: 'customer-info',
        title: 'Thông tin khách hàng',
        fields: [
          { _id: 'field-16', type: 'text', label: 'Họ và tên', order: 1, permissionProfiles: [], constraints: { isRequired: true } },
          { _id: 'field-17', type: 'phone', label: 'Số điện thoại', order: 2, permissionProfiles: [], constraints: { isRequired: true } },
          { _id: 'field-18', type: 'email', label: 'Email', order: 3, permissionProfiles: [], constraints: { isRequired: false } },
          { _id: 'field-19', type: 'date', label: 'Ngày sinh', order: 4, permissionProfiles: [], constraints: { isRequired: false } },
          { _id: 'field-20', type: 'picklist', label: 'Giới tính', order: 5, permissionProfiles: [], constraints: { picklistOptions: createFieldOptions(['Nam', 'Nữ', 'Khác']), sortOrder: 'input', isRequired: false } }
        ],
        settings: {
          sectionLayoutColumn: 'double-column' as 'double-column'
        }
      },
      {
        _id: 'skin-info',
        title: 'Thông tin da',
        fields: [
          { _id: 'field-21', type: 'picklist', label: 'Loại da', order: 1, permissionProfiles: [], constraints: { picklistOptions: createFieldOptions(['Da khô', 'Da dầu', 'Da hỗn hợp', 'Da thường']), sortOrder: 'input', isRequired: true } },
          { _id: 'field-22', type: 'checkbox', label: 'Vấn đề da', order: 2, permissionProfiles: [], constraints: { isRequired: false } },
          { _id: 'field-23', type: 'picklist', label: 'Độ nhạy cảm', order: 3, permissionProfiles: [], constraints: { picklistOptions: createFieldOptions(['Rất nhạy cảm', 'Nhạy cảm', 'Bình thường', 'Ít nhạy cảm']), sortOrder: 'input', isRequired: false } },
          { _id: 'field-24', type: 'picklist', label: 'Màu da', order: 4, permissionProfiles: [], constraints: { picklistOptions: createFieldOptions(['Trắng', 'Vàng', 'Nâu', 'Đen']), sortOrder: 'input', isRequired: false } },
          { _id: 'field-25', type: 'textarea', label: 'Mô tả chi tiết về da', order: 5, permissionProfiles: [], constraints: { textType: 'large', isRequired: false } }
        ],
        settings: {
          sectionLayoutColumn: 'double-column' as 'double-column'
        }
      },
      {
        _id: 'beauty-preferences',
        title: 'Sở thích làm đẹp',
        fields: [
          { _id: 'field-26', type: 'checkbox', label: 'Loại sản phẩm quan tâm', order: 1, permissionProfiles: [], constraints: { isRequired: false } },
          { _id: 'field-27', type: 'picklist', label: 'Thương hiệu yêu thích', order: 2, permissionProfiles: [], constraints: { picklistOptions: createFieldOptions(['L\'Oréal', 'Maybelline', 'MAC', 'Dior', 'Chanel']), sortOrder: 'input', isRequired: false } },
          { _id: 'field-28', type: 'number', label: 'Ngân sách hàng tháng', order: 3, permissionProfiles: [], constraints: { isRequired: false } },
          { _id: 'field-29', type: 'checkbox', label: 'Thành phần tránh', order: 4, permissionProfiles: [], constraints: { isRequired: false } },
          { _id: 'field-30', type: 'textarea', label: 'Mục tiêu làm đẹp', order: 5, permissionProfiles: [], constraints: { textType: 'large', isRequired: false } }
        ],
        settings: {
          sectionLayoutColumn: 'double-column' as 'double-column'
        }
      }
    ],
    supportedFieldTypes: ['text', 'phone', 'email', 'date', 'picklist', 'checkbox', 'textarea', 'number'],
    showPreviewPanel: true,
    enablePreviewMode: true,
    showToolbarActions: true,
    autoSave: false,
    autoSaveInterval: 30000,
    enableDragDrop: true,
    enableInlineEdit: true,
    enableQuickCreate: true,
    quickCreateConfig: {
      title: 'Tạo nhanh khách hàng mỹ phẩm',
      description: 'Form tạo nhanh thông tin khách hàng mỹ phẩm',
      section: {
        _id: 'qc-cosmetics-section',
        title: 'Thông tin cơ bản',
        fields: [
          // Copy từ customer-info section với _id khớp nhau
          { _id: 'field-16', type: 'text', label: 'Họ và tên', order: 1, permissionProfiles: [], constraints: { isRequired: true } },
          { _id: 'field-17', type: 'phone', label: 'Số điện thoại', order: 2, permissionProfiles: [], constraints: { isRequired: true } },
          { _id: 'field-18', type: 'email', label: 'Email', order: 3, permissionProfiles: [], constraints: { isRequired: false } },
          // Copy từ skin-info section với _id khớp nhau
          { _id: 'field-21', type: 'picklist', label: 'Loại da', order: 4, permissionProfiles: [], constraints: { picklistOptions: createFieldOptions(['Da khô', 'Da dầu', 'Da hỗn hợp', 'Da thường']), sortOrder: 'input', isRequired: true } },
          { _id: 'field-22', type: 'checkbox', label: 'Vấn đề da', order: 5, permissionProfiles: [], constraints: { isRequired: false } }
        ],
        settings: {
          sectionLayoutColumn: 'double-column' as 'double-column'
        }
      },
      enableAutoSave: false
    },
    detailViewConfig: {
      title: 'Chi tiết khách hàng mỹ phẩm',
      description: 'Hiển thị thông tin chi tiết khách hàng trong ngành mỹ phẩm',
      sections: [
        {
          _id: 'dv-customer-basic',
          name: 'Thông tin cơ bản',
          widgets: [
            { _id: 'widget-16', name: 'Thông tin cá nhân', description: 'Họ tên, SĐT, email, ngày sinh' },
            { _id: 'widget-17', name: 'Thông tin giới tính', description: 'Giới tính và độ tuổi' },
            { _id: 'widget-18', name: 'Thông tin liên hệ', description: 'Địa chỉ và cách thức liên lạc' }
          ]
        },
        {
          _id: 'dv-skin-analysis',
          name: 'Phân tích da',
          widgets: [
            { _id: 'widget-19', name: 'Loại da và đặc điểm', description: 'Phân loại da và tình trạng hiện tại' },
            { _id: 'widget-20', name: 'Vấn đề da', description: 'Các vấn đề da đang gặp phải' },
            { _id: 'widget-21', name: 'Độ nhạy cảm', description: 'Mức độ nhạy cảm và phản ứng' }
          ]
        },
        {
          _id: 'dv-beauty-profile',
          name: 'Hồ sơ làm đẹp',
          widgets: [
            { _id: 'widget-22', name: 'Sở thích sản phẩm', description: 'Loại sản phẩm quan tâm' },
            { _id: 'widget-23', name: 'Thương hiệu yêu thích', description: 'Danh sách thương hiệu ưa chuộng' },
            { _id: 'widget-24', name: 'Ngân sách làm đẹp', description: 'Mức chi tiêu hàng tháng' }
          ]
        },
        {
          _id: 'dv-product-history',
          name: 'Lịch sử sử dụng sản phẩm',
          widgets: [
            { _id: 'widget-25', name: 'Sản phẩm đã dùng', description: 'Danh sách sản phẩm đã sử dụng' },
            { _id: 'widget-26', name: 'Phản ứng và hiệu quả', description: 'Đánh giá hiệu quả sản phẩm' },
            { _id: 'widget-27', name: 'Thành phần tránh', description: 'Các thành phần gây dị ứng' }
          ]
        },
        {
          _id: 'dv-beauty-goals',
          name: 'Mục tiêu làm đẹp',
          widgets: [
            { _id: 'widget-28', name: 'Mục tiêu ngắn hạn', description: 'Mục tiêu trong 3-6 tháng' },
            { _id: 'widget-29', name: 'Mục tiêu dài hạn', description: 'Mục tiêu trong 1-2 năm' },
            { _id: 'widget-30', name: 'Kế hoạch chăm sóc', description: 'Lộ trình chăm sóc da' }
          ]
        }
      ]
    }
  },

  // Layout 3: Thực phẩm
  {
    _id: 'food-layout',
    title: 'Layout Thực Phẩm',
    name: 'Layout Thực Phẩm',
    shortDescription: 'Layout chuyên dụng cho ngành thực phẩm với các trường về dị ứng, khẩu vị',
    isDefault: false,
    createdAt: new Date('2024-01-03T00:00:00Z'),
    updatedAt: new Date('2024-01-17T09:45:00Z'),
    fieldDefaultSettings: {
      permissionProfiles: mockDefaultProfiles,
      availableSearchModules: mockFieldSettings.availableSearchModules
    },
    sections: [
      {
        _id: 'customer-basic',
        title: 'Thông tin khách hàng',
        fields: [
          { _id: 'field-31', type: 'text', label: 'Họ và tên', order: 1, permissionProfiles: [], constraints: { isRequired: true } },
          { _id: 'field-32', type: 'phone', label: 'Số điện thoại', order: 2, permissionProfiles: [], constraints: { isRequired: true } },
          { _id: 'field-33', type: 'email', label: 'Email', order: 3, permissionProfiles: [], constraints: { isRequired: false } },
          { _id: 'field-34', type: 'text', label: 'Địa chỉ giao hàng', order: 4, permissionProfiles: [], constraints: { isRequired: true } },
          { _id: 'field-35', type: 'picklist', label: 'Khu vực giao hàng', order: 5, permissionProfiles: [], constraints: { picklistOptions: createFieldOptions(['Nội thành', 'Ngoại thành', 'Tỉnh lẻ', 'Miền núi']), sortOrder: 'input', isRequired: true } }
        ],
        settings: {
          sectionLayoutColumn: 'double-column' as 'double-column'
        }
      },
      {
        _id: 'food-preferences',
        title: 'Sở thích ẩm thực',
        fields: [
          { _id: 'field-36', type: 'checkbox', label: 'Dị ứng thực phẩm', order: 1, permissionProfiles: [], constraints: { isRequired: false } },
          { _id: 'field-37', type: 'picklist', label: 'Khẩu vị', order: 2, permissionProfiles: [], constraints: { picklistOptions: createFieldOptions(['Ngọt', 'Mặn', 'Chua', 'Cay', 'Đắng']), sortOrder: 'input', isRequired: false } },
          { _id: 'field-38', type: 'checkbox', label: 'Chế độ ăn đặc biệt', order: 3, permissionProfiles: [], constraints: { isRequired: false } },
          { _id: 'field-39', type: 'picklist', label: 'Mức độ cay', order: 4, permissionProfiles: [], constraints: { picklistOptions: createFieldOptions(['Không cay', 'Ít cay', 'Vừa cay', 'Cay', 'Rất cay']), sortOrder: 'input', isRequired: false } },
          { _id: 'field-40', type: 'textarea', label: 'Yêu cầu đặc biệt', order: 5, permissionProfiles: [], constraints: { textType: 'large', isRequired: false } }
        ],
        settings: {
          sectionLayoutColumn: 'double-column' as 'double-column'
        }
      },
      {
        _id: 'delivery-preferences',
        title: 'Tùy chọn giao hàng',
        fields: [
          { _id: 'field-41', type: 'picklist', label: 'Thời gian giao hàng ưa thích', order: 1, permissionProfiles: [], constraints: { picklistOptions: createFieldOptions(['Sáng', 'Trưa', 'Chiều', 'Tối']), sortOrder: 'input', isRequired: false } },
          { _id: 'field-42', type: 'checkbox', label: 'Ngày trong tuần', order: 2, permissionProfiles: [], constraints: { isRequired: false } },
          { _id: 'field-43', type: 'picklist', label: 'Phương thức thanh toán', order: 3, permissionProfiles: [], constraints: { picklistOptions: createFieldOptions(['Tiền mặt', 'Chuyển khoản', 'Thẻ tín dụng', 'Ví điện tử']), sortOrder: 'input', isRequired: true } },
          { _id: 'field-44', type: 'checkbox', label: 'Dịch vụ bổ sung', order: 4, permissionProfiles: [], constraints: { isRequired: false } },
          { _id: 'field-45', type: 'textarea', label: 'Ghi chú giao hàng', order: 5, permissionProfiles: [], constraints: { textType: 'large', isRequired: false } }
        ],
        settings: {
          sectionLayoutColumn: 'double-column' as 'double-column'
        }
      }
    ],
    supportedFieldTypes: ['text', 'phone', 'email', 'date', 'picklist', 'checkbox', 'textarea', 'number'],
    showPreviewPanel: true,
    enablePreviewMode: true,
    showToolbarActions: true,
    autoSave: false,
    autoSaveInterval: 30000,
    enableDragDrop: true,
    enableInlineEdit: true,
    enableQuickCreate: true,
    quickCreateConfig: {
      title: 'Tạo nhanh khách hàng thực phẩm',
      description: 'Form tạo nhanh thông tin khách hàng thực phẩm',
      section: {
        _id: 'qc-food-section',
        title: 'Thông tin cơ bản',
        fields: [
          // Copy từ customer-basic section với _id khớp nhau
          { _id: 'field-31', type: 'text', label: 'Họ và tên', order: 1, permissionProfiles: [], constraints: { isRequired: true } },
          { _id: 'field-32', type: 'phone', label: 'Số điện thoại', order: 2, permissionProfiles: [], constraints: { isRequired: true } },
          { _id: 'field-34', type: 'text', label: 'Địa chỉ giao hàng', order: 3, permissionProfiles: [], constraints: { isRequired: true } },
          // Copy từ food-preferences section với _id khớp nhau
          { _id: 'field-36', type: 'checkbox', label: 'Dị ứng thực phẩm', order: 4, permissionProfiles: [], constraints: { isRequired: false } },
          { _id: 'field-37', type: 'picklist', label: 'Khẩu vị', order: 5, permissionProfiles: [], constraints: { picklistOptions: createFieldOptions(['Ngọt', 'Mặn', 'Chua', 'Cay', 'Đắng']), sortOrder: 'input', isRequired: false } }
        ],
        settings: {
          sectionLayoutColumn: 'double-column' as 'double-column'
        }
      },
      enableAutoSave: false
    },
    detailViewConfig: {
      title: 'Chi tiết khách hàng thực phẩm',
      description: 'Hiển thị thông tin chi tiết khách hàng trong ngành thực phẩm',
      sections: [
        {
          _id: 'dv-customer-info',
          name: 'Thông tin khách hàng',
          widgets: [
            { _id: 'widget-31', name: 'Thông tin cá nhân', description: 'Họ tên, SĐT, email' },
            { _id: 'widget-32', name: 'Địa chỉ giao hàng', description: 'Địa chỉ và khu vực giao hàng' },
            { _id: 'widget-33', name: 'Thông tin liên hệ', description: 'Các cách thức liên lạc' }
          ]
        },
        {
          _id: 'dv-food-profile',
          name: 'Hồ sơ ẩm thực',
          widgets: [
            { _id: 'widget-34', name: 'Dị ứng thực phẩm', description: 'Danh sách thực phẩm gây dị ứng' },
            { _id: 'widget-35', name: 'Khẩu vị yêu thích', description: 'Sở thích về vị và độ cay' },
            { _id: 'widget-36', name: 'Chế độ ăn đặc biệt', description: 'Chế độ ăn kiêng, vegetarian, vegan' }
          ]
        },
        {
          _id: 'dv-order-history',
          name: 'Lịch sử đặt hàng',
          widgets: [
            { _id: 'widget-37', name: 'Đơn hàng gần đây', description: 'Các đơn hàng trong 3 tháng qua' },
            { _id: 'widget-38', name: 'Món ăn yêu thích', description: 'Thống kê món ăn được đặt nhiều nhất' },
            { _id: 'widget-39', name: 'Tần suất đặt hàng', description: 'Thống kê tần suất và thời gian đặt' }
          ]
        },
        {
          _id: 'dv-delivery-info',
          name: 'Thông tin giao hàng',
          widgets: [
            { _id: 'widget-40', name: 'Tùy chọn giao hàng', description: 'Thời gian và phương thức giao hàng' },
            { _id: 'widget-41', name: 'Phương thức thanh toán', description: 'Các hình thức thanh toán ưa thích' },
            { _id: 'widget-42', name: 'Dịch vụ bổ sung', description: 'Các dịch vụ thêm đã sử dụng' }
          ]
        },
        {
          _id: 'dv-feedback',
          name: 'Phản hồi và đánh giá',
          widgets: [
            { _id: 'widget-43', name: 'Đánh giá món ăn', description: 'Các đánh giá và nhận xét' },
            { _id: 'widget-44', name: 'Phản hồi dịch vụ', description: 'Đánh giá về chất lượng dịch vụ' },
            { _id: 'widget-45', name: 'Góp ý cải thiện', description: 'Các đề xuất và góp ý' }
          ]
        }
      ]
    }
  }
];

export const mockLayoutRendererConfig: DynamicLayoutRendererConfig = {
  ...simpleMockLayouts[0], // Sử dụng layout đầu tiên làm base
  currentPermissionProfileId: 'admin', // Default admin permission
  showPermissionProfiles: true, // Hiển thị permission selector
  defaultView: 'view', // Default view mode
  showAllTab: true, // Hiển thị cả 2 tab view/form

  // Form Edit Mode configuration
  enableEditMode: true,
  formValues: {
    'field-1': 'Test Product Name',
    'field-xxxx-test': 'XLX',
    'field-4': new Date(),
    'field-10': 'xx',
    'field-11': 1234,
    'field-15': true
  },
  onFormSave: async (data) => {
    console.log('Form save callback called with data:', data);
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 2000));
  }
};