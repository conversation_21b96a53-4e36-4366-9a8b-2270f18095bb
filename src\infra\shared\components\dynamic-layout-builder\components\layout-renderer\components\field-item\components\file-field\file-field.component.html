@if (isVisible()) {
  <div class="file-field-container" [class.read-only]="isReadOnlyState()">
    
    <div class="field-label-container">
      <label class="field-label">
        <!-- Field icon trước label -->
        <mat-icon class="field-type-icon me-2" [style.color]="getFieldIconColor()">{{ getFieldIcon() }}</mat-icon>
        {{ config.field.label }}
        @if (isFieldRequired()) {
          <span class="required-asterisk">*</span>
        }
      </label>
      
      @if (isReadOnlyState()) {
        <mat-icon
          class="read-only-icon ms-2"
          [matTooltip]="'DYNAMIC_LAYOUT_RENDERER.FIELD_MESSAGES.READ_ONLY_TOOLTIP' | translate"
          matTooltipPosition="above">
          lock
        </mat-icon>
      }
    </div>

    <div class="field-value-container">
      
      @if (config.currentViewMode === 'view') {
        <div class="field-view-value">
          <span class="placeholder-value">{{ placeholderValue() }}</span>
        </div>
      }
      
      @else {
        <div class="file-upload-container">
          <input
            type="file"
            #fileInput
            (change)="onFileSelected($event)"
            [accept]="getAcceptedTypes()"
            [disabled]="isReadOnlyState()"
            style="display: none;">
          
          <button
            mat-raised-button
            color="primary"
            [disabled]="isReadOnly()"
            (click)="fileInput.click()"
            [class.disabled]="isReadOnly()">
            <mat-icon>{{ getFieldIcon() }}</mat-icon>
            {{ getPlaceholder() | translate }}
          </button>
          
          @if (formControl().value) {
            <div class="selected-file">
              <mat-icon class="file-icon">description</mat-icon>
              <span>{{ formControl().value }}</span>
            </div>
          }
        </div>
      }

    <!-- Field-Level Validation Errors -->
    @if (shouldShowValidationErrors()) {
      <div class="field-validation-errors" aria-live="polite">
        @for (error of getValidationErrors(); track error) {
          <div class="validation-error-item">
            <mat-icon class="error-icon">error</mat-icon>
            <span class="error-message">{{ error | translate }}</span>
          </div>
        }
      </div>
    }
    </div>

    @if (config.field.tooltip) {
      <div class="field-tooltip">
        <small class="text-muted">{{ config.field.tooltip }}</small>
      </div>
    }
  </div>
}
