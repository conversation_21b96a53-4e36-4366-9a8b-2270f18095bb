import { Component, Input, Output, EventEmitter, ChangeDetectionStrategy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule, Validators } from '@angular/forms';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatIconModule } from '@angular/material/icon';
import { MatTooltipModule } from '@angular/material/tooltip';
import { TranslateModule } from '@ngx-translate/core';

import { AbstractFieldComponent } from '../base/abstract-field.component';
import { FieldComponentInterface } from '../base/field-component.interface';
import { FieldItemConfig } from '../../../../../../models/dynamic-layout-renderer.model';
import { generatePlaceHolderData, getFieldIcon } from '../../../../../../utils/field.utils';
import { NumberField, NumberFieldTypes } from '@domain/entities/field.entity';

/**
 * Component chuyên biệt xử lý các number-based fields
 * 
 * Supported field types:
 * - number: Số nguyên
 * - decimal: Số thập phân
 * - currency: Tiền tệ với formatting
 * - percent: Phần trăm với % symbol
 * 
 * Features:
 * - View mode: Hiển thị mock data với formatting
 * - Form mode: Number input với validation
 * - Currency formatting (VND)
 * - Percent formatting với % symbol
 * - Min/max validation
 * - Step validation cho decimal
 * - Permission-based visibility và read-only state
 * - i18n support
 */
@Component({
  selector: 'app-number-field',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatFormFieldModule,
    MatInputModule,
    MatIconModule,
    MatTooltipModule,
    TranslateModule
  ],
  templateUrl: './number-field.component.html',
  styleUrls: ['./number-field.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class NumberFieldComponent extends AbstractFieldComponent implements FieldComponentInterface {

  @Input({ required: true }) config!: FieldItemConfig;

  // ===== IMPLEMENT ABSTRACT METHODS =====

  /**
   * Validate rằng field type được hỗ trợ
   */
  protected validateFieldType(): void {
    const supportedTypes: NumberFieldTypes[] = ['number', 'decimal', 'currency', 'percent'];
    if (!supportedTypes.includes(this.config.field.type as NumberFieldTypes)) {
      console.warn(`NumberFieldComponent: Unsupported field type '${this.config.field.type}'`);
    }
  }

  /**
   * Generate placeholder value dựa trên field type
   */
  protected override generatePlaceholderValue(): void {
    const fieldType = this.config.field.type as any;
    const placeHolderData = generatePlaceHolderData(fieldType, this.translateService);

    // Format placeholder data based on field type
    if (fieldType === 'currency') {
      this.placeholderValue.set(this.formatDisplayValue(Number(placeHolderData)));
    } else if (fieldType === 'percent') {
      this.placeholderValue.set(this.formatDisplayValue(Number(placeHolderData)));
    } else {
      this.placeholderValue.set(String(placeHolderData));
    }
  }

  /**
   * Lấy validators phù hợp cho field type
   * UPDATED: Sử dụng FieldValidationService tập trung
   */
  protected override getValidators(): any[] {
    return this.getAllValidators();
  }

  // ===== COMPONENT-SPECIFIC METHODS =====

  /**
   * Format giá trị để hiển thị dựa trên field type
   */
  formatDisplayValue(value: number): string {
    switch (this.config.field.type) {
      case 'currency':
        return new Intl.NumberFormat('vi-VN', {
          style: 'currency',
          currency: 'VND'
        }).format(value);

      case 'percent':
        return `${value}%`;

      case 'decimal':
        return value.toFixed(2);

      case 'number':
      default:
        return value.toString();
    }
  }

}
