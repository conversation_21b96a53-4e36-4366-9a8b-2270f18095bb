import { Component, signal, computed, OnInit, ViewChild, ElementRef, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { DragDropModule } from '@angular/cdk/drag-drop';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatIconModule } from '@angular/material/icon';
import { TranslateModule, TranslateService } from '@ngx-translate/core';

import { DynamicLayoutBuilderStateService } from '../../../../services/dynamic-layout-builder-state.service';
import { CreateTabService } from './create-tab.service';
import { FlashMessageService } from '@core/services/flash_message.service';
import { Section } from '@/shared/components/dynamic-layout-builder/models/dynamic-layout-config.dto';
import { Field } from '@domain/entities/field.entity';
import { FieldTypeSelectorComponent } from './components/field-type-selector/field-type-selector.component';
import { SectionComponent } from './components/section/section.component';
import { ResizePanelDirective } from '@shared/directives/resize-panel.directive';
import { DynamicLayoutConfigStateService } from '../../../../services/dynamic-layout-config-state.service';
import { SectionWithFields } from '../../../../models/dynamic-layout-builder.model';
import { MatButtonModule } from '@angular/material/button';

/**
 * Create Tab Component - Component độc lập xử lý Create tab
 * Chịu trách nhiệm hiển thị và quản lý giao diện tạo layout
 */
@Component({
  selector: 'app-create-tab',
  standalone: true,
  imports: [
    CommonModule,
    DragDropModule,
    MatProgressSpinnerModule,
    MatIconModule,
    TranslateModule,
    FieldTypeSelectorComponent,
    SectionComponent,
    ResizePanelDirective,
    MatButtonModule
  ],
  templateUrl: './create-tab.component.html',
  styleUrls: ['./create-tab.component.scss']
})
export class CreateTabComponent implements OnInit {
  @ViewChild('sidebar', { static: false }) sidebar!: ElementRef;
  @ViewChild('rightPanel', { static: false }) rightPanel!: ElementRef;


  // Services được provide qua factory methods từ parent component
  private stateService = inject(DynamicLayoutBuilderStateService); // UI state
  public configStateService = inject(DynamicLayoutConfigStateService); // Config & layout state (public để truy cập từ template)
  private dynamicLayoutConfigStateService = inject(DynamicLayoutConfigStateService);
  private flashMessageService = inject(FlashMessageService);
  private translateService = inject(TranslateService);

  private createTabService!: CreateTabService;


  isLoading = signal<boolean>(false);
  /** Trạng thái preview mode - từ service */
  isPreviewMode = signal<boolean>(false);

  /** Computed để kiểm tra có sections nào không */
  hasAnySections = computed(() => this.configStateService.createTabSections().length > 0);

  /** Computed để đếm tổng số fields */
  totalFields = computed(() =>
    this.configStateService.createTabSections().reduce((total: number, section: SectionWithFields) => total + section.fields.length, 0)
  );


  ngOnInit(): void {
    // Khởi tạo CreateTabService với CoreLayoutBuilderService đã inject
    this.createTabService = new CreateTabService(
      this.dynamicLayoutConfigStateService,
      this.stateService,
      this.translateService,
      this.flashMessageService
    );
  }


  onAddSection() {
    return this.createTabService.createNewSection();
  }

  onSectionDeleted(section: Section) {
    return this.createTabService.deleteSection(section);
  }

  /**
   * Xử lý khi thay đổi tiêu đề section
   * ✅ REFACTORED: Sử dụng CreateTabService để xử lý logic
   */
  onSectionTitleChanged(data: { section: Section; newTitle: string }) {
    return this.createTabService.updateSectionTitle(
      data.section,
      data.newTitle
    );
  }

  /**
   * Xử lý khi sắp xếp lại fields trong section
   * ✅ REFACTORED: Sử dụng CreateTabService để xử lý logic
   */
  onSectionFieldsReordered(data: { section: Section; fields: Field[] }) {
    return this.createTabService.reorderFieldsInSection(
      data.section,
      data.fields
    );
  }

  /**
   * Xử lý khi toggle required field
   * ✅ REFACTORED: Sử dụng CreateTabService để xử lý logic
   */
  onSectionFieldRequiredToggled(data: { section: Section; field: Field; isRequired: boolean }) {
    return this.createTabService.toggleFieldRequired(data);
  }

  /**
   * Xử lý khi chỉnh sửa properties field
   * ✅ REFACTORED: Sử dụng CreateTabService để xử lý logic
   */
  onSectionFieldPropertiesEdit(data: { section: Section; field: Field }) {
    return this.createTabService.updateFieldProperties(data);
  }

  /**
   * Xử lý khi set permissions field
   * ✅ REFACTORED: Sử dụng CreateTabService để xử lý logic
   */
  onSectionFieldPermissionSet(data: { section: Section; field: Field }) {
    return this.createTabService.updateFieldPermissions(data);
  }

  /**
   * Xử lý khi xóa field
   * ✅ REFACTORED: Sử dụng CreateTabService để xử lý logic
   */
  onSectionFieldDeleted(data: { section: Section; field: Field }) {
    return this.createTabService.deleteFieldFromSection(data);
  }

  /**
   * ✅ NEW: Xử lý khi field label thay đổi
   */
  onSectionFieldLabelChanged(data: { section: Section; field: Field; newLabel: string }) {
    return this.createTabService.updateFieldProperties({
      ...data,
      field: {
        ...data.field,
        label: data.newLabel
      }
    });
  }

  /**
   * ✅ NEW: Xử lý khi section layout column thay đổi
   */
  onSectionLayoutChanged(data: { section: Section; layoutColumn: 'single-column' | 'double-column' }) {
    return this.createTabService.updateSectionLayout(data.section, data.layoutColumn);
  }

  /**
   * Xử lý khi quick add field
   * ✅ REFACTORED: Xử lý internally thay vì emit event
   */
  onSectionQuickAddField(data: { section: Section; fieldType: string }) {
    return this.createTabService.quickAddFieldToSection(data);
  }


  // ==================== UTILITY METHODS ====================

  /**
   * TrackBy function cho sections
   * UI Logic: Tối ưu hiệu suất rendering
   */
  trackBySection(_index: number, section: Section): string {
    return section._id || _index.toString();
  }

  /**
   * TrackBy function cho fields
   * UI Logic: Tối ưu hiệu suất rendering
   */
  trackByField(_index: number, field: Field): string | number {
    return field._id || _index;
  }
}
