import { Component, Input, Output, EventEmitter, signal, inject, OnInit, OnChanges, SimpleChanges, WritableSignal, ElementRef, ViewChild } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatMenuModule } from '@angular/material/menu';
import { MatInputModule } from '@angular/material/input';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatDividerModule } from '@angular/material/divider';
import { TranslateModule, TranslateService } from '@ngx-translate/core';

// Modal Services
import { FieldPropertiesModalService } from '@/shared/components/dynamic-layout-builder/modals/field-properties/field-properties-modal.service';
import { FieldPermissionModalService } from '@/shared/components/dynamic-layout-builder/modals/field-permission/field-permission-modal.service';
import { ConfirmModalService } from '@shared/modals/common/confirm-modal/confirm-modal.service';
import { DynamicLayoutConfigStateService } from '@/shared/components/dynamic-layout-builder/services/dynamic-layout-config-state.service';

// Modal Data Types
import { ConfirmModalData } from '@shared/modals/common/confirm-modal/confirm-modal.component';
import { FieldPropertiesData } from '@/shared/components/dynamic-layout-builder/modals/field-properties/field-properties-modal.component';
import { Field, FieldType } from '@domain/entities/field.entity';
import { getFieldIcon, getFieldTypeLabel, getFieldIconColor, getFieldBackgroundColor } from '@/shared/components/dynamic-layout-builder/utils/field.utils';
import { MatTooltip } from '@angular/material/tooltip';

/**
 * Component hiển thị một field item trong bảng
 * Hỗ trợ inline editing cho label và menu actions
 */
@Component({
  selector: 'app-field-item',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    MatIconModule,
    MatButtonModule,
    MatMenuModule,
    MatInputModule,
    MatFormFieldModule,
    MatDividerModule,
    TranslateModule,
    MatTooltip
  ],
  templateUrl: './field-item.component.html',
  styleUrls: ['./field-item.component.scss']
})
export class FieldItemComponent implements OnInit, OnChanges {
  /**
   * Inject modal services và config state service
   */
  private translateService = inject(TranslateService);
  private fieldPropertiesModalService = inject(FieldPropertiesModalService);
  private fieldPermissionModalService = inject(FieldPermissionModalService);
  private confirmModalService = inject(ConfirmModalService);

  /**
   * Field data input với thông tin bổ sung từ parent component
   * permissionProfiles đã được di chuyển vào trong field.permissionProfiles
   */
  @Input({ required: true }) data!: {
    field: Field;
    availableSearchModules: FieldPropertiesData['availableSearchModules'];
  };

  /**
   * Field signal để reactive update UI
   */
  field: WritableSignal<Field> = signal(null as any);

  /**
   * Event khi label thay đổi
   */
  @Output() labelChanged = new EventEmitter<{ field: Field; newLabel: string }>();

  /**
   * Event khi toggle required status
   */
  @Output() requiredToggled = new EventEmitter<{ field: Field; isRequired: boolean }>();

  /**
   * Event khi edit properties
   */
  @Output() propertiesEdit = new EventEmitter<Field>();

  /**
   * Event khi set permission
   */
  @Output() permissionSet = new EventEmitter<Field>();

  /**
   * Event khi delete field
   */
  @Output() fieldDeleted = new EventEmitter<Field>();


  @ViewChild('labelInput') labelInput!: ElementRef<HTMLElement>;

  /**
   * Signal để quản lý trạng thái đang edit label
   */
  isEditingLabel = signal(false);

  /**
   * Signal để quản lý trạng thái đang drag
   */
  isDragging = signal(false);

  /**
   * Label đang được edit
   */
  editingLabel = '';

  /**
   * Lifecycle: Component initialization
   */
  ngOnInit(): void {
    // Sync field signal với data.field input lần đầu
    this.field.set(this.data.field);
  }

  /**
   * Lifecycle: Input changes
   */
  ngOnChanges(changes: SimpleChanges): void {
    // Sync field signal khi data input thay đổi
    if (changes['data'] && changes['data'].currentValue) {
      this.field.set(changes['data'].currentValue.field);
    }
  }

  /**
   * Bắt đầu edit label
   */
  startEditLabel(): void {
    this.editingLabel = this.field().label;
    this.isEditingLabel.set(true);

    // Focus vào input sau khi render
    setTimeout(() => {
      if (this.labelInput?.nativeElement) {
        this.labelInput.nativeElement.focus();
      }
    });
  }

  /**
   * Lưu label mới
   */
  saveLabel(): void {
    if (this.editingLabel.trim() && this.editingLabel.trim() !== this.field().label) {
      this.labelChanged.emit({
        field: this.field(),
        newLabel: this.editingLabel.trim()
      });
    }
    this.isEditingLabel.set(false);
  }

  /**
   * Hủy edit label
   */
  cancelEditLabel(): void {
    this.editingLabel = '';
    this.isEditingLabel.set(false);
  }

  /**
   * Kiểm tra xem field có required không từ constraints
   */
  isFieldRequired(): boolean {
    const fieldConfig = this.field();
    return fieldConfig.constraints && 'isRequired' in fieldConfig.constraints
      ? (fieldConfig.constraints as any).isRequired
      : false;
  }

  /**
   * Toggle required status
   */
  onToggleRequired(): void {
    this.requiredToggled.emit({
      field: this.field(),
      isRequired: !this.isFieldRequired()
    });
  }

  /**
   * Mở modal edit properties - sử dụng dữ liệu từ parent component
   */
  async onEditProperties(): Promise<void> {
    try {
      // Mở modal và nhận kết quả - sử dụng availableSearchModules từ data input
      const result = await this.fieldPropertiesModalService.open({
        field: this.field(),
        availableSearchModules: this.data.availableSearchModules
      });

      if (result) {
        // Cập nhật field hiện tại với properties mới
        const updatedField = {
          ...this.field(),
          label: result.label,
          type: result.type,
          value: result.value,
          description: result.tooltip,
          constraints: result.constraints
        };

        // Cập nhật field signal để trigger UI update
        this.field.set(updatedField as Field);

        // Cập nhật data.field input
        this.data.field = updatedField as Field;

        // Emit event với field đã được cập nhật
        this.propertiesEdit.emit(updatedField as Field);
      }
    } catch (error) {
      console.error('❌ Error opening field properties modal:', error);
    }
  }

  /**
   * Mở modal set permission - sử dụng dữ liệu từ parent component
   */
  async onSetPermission(): Promise<void> {
    try {
      // Mở modal với Field object và nhận kết quả
      const result = await this.fieldPermissionModalService.open({
        field: this.field()
      });

      if (result && result.length > 0) {
        // Cập nhật field hiện tại với permission profiles mới
        const updatedField = {
          ...this.field(),
          permissionProfiles: [...result]
        };

        // Cập nhật field signal để trigger UI update
        this.field.set(updatedField as Field);

        // Cập nhật data.field input
        this.data.field = updatedField as Field;

        // Emit event với field đã được cập nhật
        this.permissionSet.emit(updatedField as Field);
      } 
    } catch (error) {
      // console.error('❌ Error opening field permission modal:', error);
    }
  }

  /**
   * Xóa field
   */
  async onDeleteField(): Promise<void> {
    try {
      // Tạo dữ liệu cho modal xác nhận
      const confirmData: ConfirmModalData = {
        title: 'DYNAMIC_LAYOUT_BUILDER.CONFIRM_DELETE_FIELD_TITLE',
        message: `DYNAMIC_LAYOUT_BUILDER.CONFIRM_DELETE_FIELD_MESSAGE`,
        confirmText: 'COMMON.DELETE',
        cancelText: 'COMMON.CANCEL',
        confirmColor: 'warn'
      };

      // Mở modal xác nhận và nhận kết quả
      const confirmed = await this.confirmModalService.confirm(confirmData);

      if (confirmed) {
        // User xác nhận xóa - emit event với field hiện tại
        this.fieldDeleted.emit(this.field());
        // console.log('✅ Field deleted:', this.field().label);
      } else {
        // User hủy bỏ - không làm gì
        // console.log('❌ Field deletion cancelled');
      }
    } catch (error) {
      // console.error('❌ Error opening confirm modal:', error);
    }
  }

  /**
   * Bắt đầu drag field
   * Được gọi khi user bắt đầu kéo field item
   */
  onDragStart(): void {
    this.isDragging.set(true);
    // console.log('🚀 Field drag started:', this.field().label);
  }

  /**
   * Kết thúc drag field
   * Được gọi khi user thả field item
   */
  onDragEnd(): void {
    this.isDragging.set(false);
    // console.log('🏁 Field drag ended:', this.field().label);
  }

  /**
   * Lấy icon cho field type (đồng bộ với sidebar field selector)
   */
  getFieldIcon(fieldType: FieldType): string {
    return getFieldIcon(fieldType);
  }

  /**
   * Lấy label hiển thị cho field type
   */
  getFieldTypeLabel(type: FieldType): string {
    return getFieldTypeLabel(type, this.translateService);
  }

  /**
   * Lấy màu sắc icon cho field type
   */
  getFieldIconColor(fieldType: FieldType): string {
    return getFieldIconColor(fieldType);
  }

  /**
   * Lấy màu sắc background cho field type
   */
  getFieldBackgroundColor(fieldType: FieldType): string {
    return getFieldBackgroundColor(fieldType);
  }
}
