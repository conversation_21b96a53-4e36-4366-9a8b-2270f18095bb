<div class="field-list-container">
  <!-- Danh sách fields với SortableJS drag & drop - 2 Column Layout -->
  <div
    class="field-list"
    (dragover)="onExternalDragOver($event)"
    (dragenter)="onExternalDragEnter($event)"
    (dragleave)="onExternalDragLeave($event)"
    (drop)="onExternalDrop($event)">

    <!-- Field Grid Container (Angular CDK Drop List) -->
    <div
      class="field-grid"
      [class.single-column]="sectionLayoutColumn() === 'single-column'"
      [class.double-column]="sectionLayoutColumn() === 'double-column'"
         cdkDropList
         [cdkDropListData]="fields()"
         (cdkDropListDropped)="onFieldDrop($event)"
        cdkDropListOrientation="mixed"
         [id]="'field-grid-' + sectionId()">

      <!-- Field items sử dụng FieldItemComponent (Angular CDK Drag items) -->
      <div
        *ngFor="let field of fields(); trackBy: trackByField; let i = index"
        class="field-grid-item"
        cdkDrag
        [cdkDragData]="field"
        [attr.data-field-id]="field._id"
        [attr.data-field-index]="i">

        <!-- Sử dụng FieldItemComponent với drag & drop events -->
        <app-field-item
          [data]="{
            field: field,
            availableSearchModules: availableSearchModules()
          }"
          (labelChanged)="onFieldLabelChanged($event)"
          (requiredToggled)="onFieldRequiredToggled($event)"
          (propertiesEdit)="onEditProperties($event)"
          (permissionSet)="onSetPermission($event)"
          (fieldDeleted)="onDeleteField($event)">
        </app-field-item>
      </div>

      <!-- External drop zone (for dropping from sidebar) -->
      <div class="external-drop-zone"
           [class.active]="isDragOver() && isExternalDrag"
           (dragover)="onExternalDragOver($event)"
           (dragenter)="onExternalDragEnter($event)"
           (dragleave)="onExternalDragLeave($event)"
           (drop)="onExternalDrop($event)"
           >
        <div class="drop-indicator">
          <mat-icon>add</mat-icon>
          <span>{{ 'DYNAMIC_LAYOUT_BUILDER.DROP_HERE' | translate }}</span>
        </div>
      </div>
    </div>
  </div>

  <!-- Empty state with drop zone and quick add buttons -->
  <div class="empty-state" *ngIf="fields().length === 0"
       (dragover)="onExternalDragOver($event)"
       (dragenter)="onExternalDragEnter($event)"
       (dragleave)="onExternalDragLeave($event)"
       (drop)="onExternalDrop($event)">
    <mat-icon class="empty-icon">add_box</mat-icon>
    <p class="empty-message">
      {{ 'DYNAMIC_LAYOUT_BUILDER.SECTION.DROP_FIELDS_HERE' | translate }}
    </p>

    <!-- Quick Add Field Buttons -->
    <div class="quick-add-buttons mt-3">
      <button mat-stroked-button
              color="primary"
              class="me-2 mb-2"
              (click)="onQuickAddField('text')">
        <mat-icon>text_fields</mat-icon>
        {{ 'DYNAMIC_LAYOUT_BUILDER.TEXT' | translate }}
      </button>
      <button mat-stroked-button
              color="primary"
              class="me-2 mb-2"
              (click)="onQuickAddField('number')">
        <mat-icon>numbers</mat-icon>
        {{ 'DYNAMIC_LAYOUT_BUILDER.NUMBER' | translate }}
      </button>
      <button mat-stroked-button
              color="primary"
              class="me-2 mb-2"
              (click)="onQuickAddField('email')">
        <mat-icon>email</mat-icon>
        {{ 'DYNAMIC_LAYOUT_BUILDER.EMAIL' | translate }}
      </button>
    </div>
  </div>
</div>
