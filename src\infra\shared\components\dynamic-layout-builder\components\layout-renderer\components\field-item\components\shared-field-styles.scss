/**
 * Shared Field Styles for Dynamic Layout Builder Field Components
 * 
 * Chứa CSS chung cho tất cả field components để đảm bảo tính nhất quán
 * về styling, đặc biệt là icon trong label và validation error display
 */

// ===== FIELD LABEL ICON STYLING =====
.field-label {
  display: flex;
  align-items: center;
  
  // Icon trong label - được thêm sau refactor
  .field-type-icon {
    // Kích thước icon phù hợp với text
    font-size: 1rem !important;
    width: 1rem !important;
    height: 1rem !important;
    
    // Vertical alignment với text
    display: inline-flex;
    align-items: center;
    justify-content: center;
    
    // Margin để tạo khoảng cách với text
    margin-right: 0.5rem;
    
    // Đảm bảo icon không bị shrink
    flex-shrink: 0;
    
    // Line height để align với text
    line-height: 1;
    
    // Vertical align fallback cho các browser cũ
    vertical-align: middle;
  }
  
  // Required asterisk styling
  .required-asterisk {
    color: var(--bs-danger);
    margin-left: 0.25rem;
    font-weight: bold;
  }
}

// ===== FIELD VALIDATION ERROR STYLING =====
.field-validation-errors {
  margin-top: 0.25rem;
  
  .validation-error-item {
    display: flex;
    align-items: flex-start;
    color: var(--bs-danger);
    font-size: 0.75rem;
    line-height: 1.3;
    margin-bottom: 0.125rem;
    
    &:last-child {
      margin-bottom: 0;
    }
    
    // Error icon
    .error-icon {
      font-size: 0.875rem !important;
      width: 0.875rem !important;
      height: 0.875rem !important;
      margin-right: 0.25rem;
      margin-top: 0.0625rem; // Slight offset để align với text
      flex-shrink: 0;
    }
    
    // Error message text
    .error-message {
      flex: 1;
    }
  }
}

// ===== RESPONSIVE ADJUSTMENTS =====
@media (max-width: 576px) {
  .field-label {
    .field-type-icon {
      font-size: 0.875rem !important;
      width: 0.875rem !important;
      height: 0.875rem !important;
      margin-right: 0.375rem;
    }
  }
  
  .field-validation-errors {
    .validation-error-item {
      font-size: 0.6875rem;
      
      .error-icon {
        font-size: 0.75rem !important;
        width: 0.75rem !important;
        height: 0.75rem !important;
      }
    }
  }
}

// ===== ACCESSIBILITY IMPROVEMENTS =====
.field-label {
  // Đảm bảo label có thể focus được khi cần
  &:focus-within {
    outline: 2px solid var(--bs-primary);
    outline-offset: 2px;
    border-radius: 0.25rem;
  }
}

.field-validation-errors {
  // ARIA live region để screen reader đọc errors
  &[aria-live] {
    .validation-error-item {
      // Đảm bảo error messages có contrast tốt
      &::before {
        content: "⚠️";
        margin-right: 0.25rem;
        speak: never; // Không đọc emoji
      }
    }
  }
}

// ===== DARK THEME SUPPORT =====
@media (prefers-color-scheme: dark) {
  .field-label {
    .field-type-icon {
      // Icon color sẽ được set bởi getFieldIconColor() method
      // Chỉ cần đảm bảo contrast tốt trong dark mode
      opacity: 0.9;
    }
  }
  
  .field-validation-errors {
    .validation-error-item {
      color: #ff6b6b; // Màu đỏ sáng hơn cho dark mode
      
      .error-icon {
        color: #ff6b6b;
      }
    }
  }
}

// ===== ANIMATION EFFECTS =====
.field-validation-errors {
  // Smooth animation khi validation errors xuất hiện/biến mất
  transition: all 0.2s ease-in-out;
  
  .validation-error-item {
    // Fade in animation
    animation: fadeInError 0.3s ease-in-out;
  }
}

@keyframes fadeInError {
  from {
    opacity: 0;
    transform: translateY(-0.25rem);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// ===== PRINT STYLES =====
@media print {
  .field-label {
    .field-type-icon {
      // Ẩn icon khi in để tiết kiệm mực
      display: none;
    }
  }
  
  .field-validation-errors {
    // Hiển thị errors khi in để debug
    color: #000 !important;
    
    .validation-error-item {
      .error-icon {
        display: none;
      }
    }
  }
}
