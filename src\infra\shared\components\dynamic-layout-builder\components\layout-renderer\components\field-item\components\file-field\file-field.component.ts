import { Component, Input, Output, EventEmitter, ChangeDetectionStrategy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatTooltipModule } from '@angular/material/tooltip';
import { TranslateModule } from '@ngx-translate/core';

import { AbstractFieldComponent } from '../base/abstract-field.component';
import { FieldComponentInterface } from '../base/field-component.interface';
import { FieldItemConfig } from '../../../../../../models/dynamic-layout-renderer.model';
import { FieldValue, FileFieldTypes } from '@domain/entities/field.entity';
import { getFieldIconColor, generatePlaceHolderData, getFieldIcon } from '../../../../../../utils/field.utils';

@Component({
  selector: 'app-file-field',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatButtonModule,
    MatIconModule,
    MatTooltipModule,
    TranslateModule
  ],
  templateUrl: './file-field.component.html',
  styleUrls: ['./file-field.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class FileFieldComponent extends AbstractFieldComponent implements FieldComponentInterface {

  @Input({ required: true }) config!: FieldItemConfig;

  // ===== IMPLEMENT ABSTRACT METHODS =====

  /**
   * Validate rằng field type được hỗ trợ
   */
  protected validateFieldType(): void {
    const supportedTypes: FileFieldTypes[] = ['file', 'image'];
    if (!supportedTypes.includes(this.config.field.type as FileFieldTypes)) {
      console.warn(`FileFieldComponent: Unsupported field type '${this.config.field.type}'`);
    }
  }

  /**
   * Generate placeholder value dựa trên field type
   */
  protected override generatePlaceholderValue(): void {
    const placeHolderData = generatePlaceHolderData(this.config.field.type, this.translateService);
    this.placeholderValue.set(String(placeHolderData));
  }

  /**
   * Lấy validators phù hợp cho field type
   */
  protected override getValidators(): any[] {
    return this.getCommonValidators();
  }


  // ===== COMPONENT-SPECIFIC METHODS =====

  /**
   * Xử lý khi file được chọn
   */
  onFileSelected(event: Event): void {
    const input = event.target as HTMLInputElement;
    if (input.files && input.files.length > 0) {
      const file = input.files[0];
      this.onValueChange!(file.name);
    }
  }

  /**
   * Lấy accepted file types
   */
  getAcceptedTypes(): string {
    if (this.config.field.type === 'image') {
      return 'image/*';
    }
    return '*/*';
  }
}
