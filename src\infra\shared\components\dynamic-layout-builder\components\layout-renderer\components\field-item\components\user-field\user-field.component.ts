import { Component, Input, Output, EventEmitter, ChangeDetectionStrategy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule } from '@angular/forms';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatSelectModule } from '@angular/material/select';
import { MatIconModule } from '@angular/material/icon';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatChipsModule } from '@angular/material/chips';
import { TranslateModule } from '@ngx-translate/core';

import { AbstractFieldComponent } from '../base/abstract-field.component';
import { FieldComponentInterface } from '../base/field-component.interface';
import { FieldItemConfig } from '../../../../../../models/dynamic-layout-renderer.model';
import { FieldValue } from '@domain/entities/field.entity';
import { generatePlaceHolderData } from '../../../../../../utils/field.utils';

@Component({
  selector: 'app-user-field',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatFormFieldModule,
    MatSelectModule,
    MatIconModule,
    MatTooltipModule,
    MatChipsModule,
    TranslateModule
  ],
  templateUrl: './user-field.component.html',
  styleUrls: ['./user-field.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class UserFieldComponent extends AbstractFieldComponent implements FieldComponentInterface {

  @Input({ required: true }) config!: FieldItemConfig;

  // ===== IMPLEMENT ABSTRACT METHODS =====

  /**
   * Validate rằng field type được hỗ trợ
   */
  protected validateFieldType(): void {
    if (this.config.field.type !== 'user') {
      console.warn(`UserFieldComponent: Unsupported field type '${this.config.field.type}'`);
    }
  }

  /**
   * Generate placeholder value dựa trên field type
   */
  protected override generatePlaceholderValue(): void {
    const placeHolderData = generatePlaceHolderData('user', this.translateService);
    this.placeholderValue.set(String(placeHolderData));
  }

  /**
   * Lấy validators phù hợp cho field type
   */
  protected override getValidators(): any[] {
    return this.getCommonValidators();
  }

  // ===== COMPONENT-SPECIFIC METHODS =====

  /**
   * Lấy danh sách users mock
   */
  getUserOptions(): Array<{ value: string; label: string }> {
    return [
      { value: 'user1', label: 'John Doe' },
      { value: 'user2', label: 'Jane Smith' },
      { value: 'user3', label: 'Bob Johnson' }
    ];
  }
}
