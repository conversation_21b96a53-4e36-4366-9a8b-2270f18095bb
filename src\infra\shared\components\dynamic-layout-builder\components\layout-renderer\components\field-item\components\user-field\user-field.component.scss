// Import shared field styles
@use '../shared-field-styles.scss';

// User Field Component Styles
.user-field-container {
  width: 100%;
  margin-bottom: 1rem;

  .field-label-container {
    display: flex;
    align-items: center;
    margin-bottom: 0.5rem;

    .field-label {
      font-weight: 500;
      color: var(--bs-body-color);
      margin: 0;
      font-size: 0.875rem;

      .required-asterisk {
        color: var(--bs-danger);
        margin-left: 0.25rem;
      }
    }

    .read-only-icon {
      font-size: 1rem;
      width: 1rem;
      height: 1rem;
      color: var(--bs-warning);
    }
  }

  .field-value-container {
    width: 100%;

    .field-view-value {
      display: flex;
      align-items: center;
      padding: 0.75rem;
      background-color: var(--bs-light);
      border: 1px solid var(--bs-border-color);
      border-radius: 0.375rem;
      min-height: 3.5rem;

      .field-type-icon {
        color: var(--bs-secondary);
        font-size: 1.25rem;
        width: 1.25rem;
        height: 1.25rem;
      }

      .user-info {
        display: flex;
        flex-direction: column;

        .user-name {
          color: var(--bs-body-color);
          font-size: 0.875rem;
          font-weight: 500;
        }

        .user-email {
          font-size: 0.75rem;
          margin-top: 0.125rem;
        }
      }
    }

    ::ng-deep .mat-mdc-form-field {
      width: 100%;

      .field-type-icon {
        color: var(--bs-secondary);
        font-size: 1.25rem;
        width: 1.25rem;
        height: 1.25rem;
      }

      &.mat-form-field-invalid {
        .field-type-icon {
          color: var(--bs-danger);
        }
      }
    }
  }

  .field-tooltip {
    margin-top: 0.25rem;
    
    small {
      font-size: 0.75rem;
      line-height: 1.2;
    }
  }

  &.read-only {
    opacity: 0.8;

    .field-label {
      color: var(--bs-secondary);
    }
  }
}

// User option styling in dropdown
::ng-deep .mat-mdc-option {
  .user-option {
    display: flex;
    flex-direction: column;
    align-items: flex-start;

    .user-name {
      font-weight: 500;
      color: var(--bs-body-color);
    }

    .user-email {
      font-size: 0.75rem;
      color: var(--bs-secondary);
      margin-top: 0.125rem;
    }
  }
}
