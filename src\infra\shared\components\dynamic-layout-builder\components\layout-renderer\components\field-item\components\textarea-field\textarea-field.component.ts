import { Component, Input, Output, EventEmitter, ChangeDetectionStrategy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule, Validators } from '@angular/forms';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatIconModule } from '@angular/material/icon';
import { MatTooltipModule } from '@angular/material/tooltip';
import { TranslateModule } from '@ngx-translate/core';

import { AbstractFieldComponent } from '../base/abstract-field.component';
import { FieldComponentInterface } from '../base/field-component.interface';
import { FieldItemConfig } from '../../../../../../models/dynamic-layout-renderer.model';
import { FieldValue, TextField } from '@domain/entities/field.entity';
import { generatePlaceHolderData } from '../../../../../../utils/field.utils';

/**
 * Component chuyên biệt xử lý textarea fields
 * 
 * Features:
 * - View mode: Hi<PERSON><PERSON> thị mock data với line breaks
 * - Form mode: Textarea với auto-resize
 * - Character count display
 * - Min/max length validation
 * - Permission-based visibility và read-only state
 * - i18n support
 */
@Component({
  selector: 'app-textarea-field',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatFormFieldModule,
    MatInputModule,
    MatIconModule,
    MatTooltipModule,
    TranslateModule
  ],
  templateUrl: './textarea-field.component.html',
  styleUrls: ['./textarea-field.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class TextareaFieldComponent extends AbstractFieldComponent implements FieldComponentInterface {

  @Input({ required: true }) config!: FieldItemConfig;

  // ===== IMPLEMENT ABSTRACT METHODS =====

  /**
   * Validate rằng field type được hỗ trợ
   */
  protected validateFieldType(): void {
    if (this.config.field.type !== 'textarea') {
      console.warn(`TextareaFieldComponent: Unsupported field type '${this.config.field.type}'`);
    }
  }

  /**
   * Generate placeholder value dựa trên field type
   */
  protected override generatePlaceholderValue(): void {
    this.placeholderValue.set(generatePlaceHolderData('textarea', this.translateService) as string);
  }

  /**
   * Lấy validators phù hợp cho field type
   * UPDATED: Sử dụng FieldValidationService tập trung
   */
  protected override getValidators(): any[] {
    return this.getAllValidators();
  }



  // ===== COMPONENT-SPECIFIC METHODS =====

  /**
   * Lấy character count cho textarea
   */
  getCharacterCount(): number {
    return this.formControl().value?.length || 0;
  }

  /**
   * Lấy max length từ constraints
   */
  getMaxLength(): string {
    const constraints = this.config.field.constraints as any;
    return (constraints?.maxLength && String(constraints?.maxLength)) || '∞';
  }
}
