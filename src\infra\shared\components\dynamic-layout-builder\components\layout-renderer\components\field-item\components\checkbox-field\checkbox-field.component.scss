// Import shared field styles
@use '../shared-field-styles.scss';



// Checkbox Field Component Styles
.checkbox-field-container {
  width: 100%;
  margin-bottom: 1rem;

  .field-label-container {
    display: flex;
    align-items: center;
    margin-bottom: 0.5rem;

    .field-label {
      font-weight: 500;
      color: var(--bs-body-color);
      margin: 0;
      font-size: 0.875rem;

      .required-asterisk {
        color: var(--bs-danger);
        margin-left: 0.25rem;
      }
    }

    .read-only-icon {
      font-size: 1rem;
      width: 1rem;
      height: 1rem;
      color: var(--bs-warning);
    }
  }

  .field-value-container {
    width: 100%;

    .field-view-value {
      display: flex;
      align-items: center;
      padding: 0.75rem;
      background-color: var(--bs-light);
      border: 1px solid var(--bs-border-color);
      border-radius: 0.375rem;
      min-height: 3.5rem;

      .checkbox-icon {
        font-size: 1.5rem;
        width: 1.5rem;
        height: 1.5rem;
        color: var(--bs-secondary);

        &.checked {
          color: var(--bs-success);
        }
      }

      .checkbox-label {
        color: var(--bs-body-color);
        font-size: 0.875rem;
        flex: 1;
      }

      .checkbox-status {
        font-size: 0.75rem;
        font-weight: 500;
        color: var(--bs-secondary);

        &.checked {
          color: var(--bs-success);
        }
      }
    }

    .checkbox-form-container {
      padding: 0.5rem 0;

      .field-checkbox {
        ::ng-deep .mat-mdc-checkbox {
          .mdc-checkbox {
            .mdc-checkbox__native-control:enabled:not(:checked):not(:indeterminate):not([data-indeterminate=true]) ~ .mdc-checkbox__background {
              border-color: var(--bs-border-color);
            }

            .mdc-checkbox__native-control:enabled:checked ~ .mdc-checkbox__background,
            .mdc-checkbox__native-control:enabled:indeterminate ~ .mdc-checkbox__background {
              background-color: var(--bs-primary);
              border-color: var(--bs-primary);
            }
          }

          .mdc-form-field {
            color: var(--bs-body-color);
            font-size: 0.875rem;
          }

          &.mat-mdc-checkbox-disabled {
            .mdc-form-field {
              color: var(--bs-secondary);
            }
          }
        }
      }

      .checkbox-error {
        margin-top: 0.25rem;
        margin-left: 2rem;

        small {
          font-size: 0.75rem;
        }
      }
    }
  }

  .field-tooltip {
    margin-top: 0.25rem;
    
    small {
      font-size: 0.75rem;
      line-height: 1.2;
    }
  }

  &.read-only {
    opacity: 0.8;

    .field-label {
      color: var(--bs-secondary);
    }
  }
}

// Responsive adjustments
@media (max-width: 576px) {
  .checkbox-field-container {
    margin-bottom: 0.75rem;

    .field-label-container {
      margin-bottom: 0.375rem;

      .field-label {
        font-size: 0.8125rem;
      }
    }

    .field-value-container {
      .field-view-value {
        padding: 0.625rem;
        min-height: 3rem;

        .checkbox-icon {
          font-size: 1.375rem;
          width: 1.375rem;
          height: 1.375rem;
        }

        .checkbox-label {
          font-size: 0.8125rem;
        }

        .checkbox-status {
          font-size: 0.6875rem;
        }
      }
    }
  }
}

// Dark theme support
@media (prefers-color-scheme: dark) {
  .checkbox-field-container {
    .field-value-container {
      .field-view-value {
        background-color: var(--bs-dark);
        border-color: var(--bs-border-color-translucent);
      }
    }
  }
}
