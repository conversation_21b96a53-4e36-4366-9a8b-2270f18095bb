import { Injectable, inject } from '@angular/core';
import { ResponsiveModalService } from '@core/services/responsive-modal.service';
import { TranslateService } from '@ngx-translate/core';
import { DynamicLayoutRendererModalComponent } from './dynamic-layout-renderer-modal.component';
import {
  DynamicLayoutRendererModalData,
  DynamicLayoutRendererModalResult
} from '../../models/dynamic-layout-renderer.model';

/**
 * Service để mở DynamicLayoutRendererModal sử dụng ResponsiveModalService
 * 
 * Chức năng:
 * - Wrap việc mở modal với ResponsiveModalService.open
 * - Type safety với DynamicLayoutRendererModalData và DynamicLayoutRendererModalResult
 * - Cấu hình modal phù hợp cho việc hiển thị layout renderer
 * - Hỗ trợ cả View mode và Form Edit mode
 */
@Injectable({
  providedIn: 'root'
})
export class DynamicLayoutRendererModalService {
  private responsiveModalService = inject(ResponsiveModalService);
  private translateService = inject(TranslateService);

  /**
   * Mở modal hiển thị layout renderer
   * 
   * @param data Dữ liệu đầu vào cho modal
   * @returns Promise<DynamicLayoutRendererModalResult | undefined> Kết quả từ modal
   */
  async open(data: DynamicLayoutRendererModalData): Promise<DynamicLayoutRendererModalResult | undefined> {
    try {
      const modalConfig = {
        data,
        width: data.width || '90vw',
        maxWidth: '1200px',
        minWidth: '600px',
        height: data.height || 'auto',
        maxHeight: '90vh',
        disableClose: data.disableClose || false,
        hasBackdrop: true,
        enableClose: data.enableClose !== false,
        actions: {
          useDefault: false // Sử dụng custom actions trong component
        }
      };


      const result = await this.responsiveModalService.open<
        DynamicLayoutRendererModalData,
        DynamicLayoutRendererModalResult,
        DynamicLayoutRendererModalComponent
      >(DynamicLayoutRendererModalComponent, modalConfig);

      return result;

    } catch (error) {
      console.error(this.translateService.instant('DYNAMIC_LAYOUT_RENDERER_MODAL.DEBUG.ERROR_OPENING'), error);
      return undefined;
    }
  }

  /**
   * Mở modal trong View mode (read-only)
   * 
   * @param data Dữ liệu layout renderer
   * @param options Tùy chọn modal (title, size, etc.)
   * @returns Promise<DynamicLayoutRendererModalResult | undefined>
   */
  async openViewMode(
    data: Omit<DynamicLayoutRendererModalData, 'enableEditMode' | 'title'>,
    options?: {
      title?: string;
      width?: string;
      height?: string;
      disableClose?: boolean;
    }
  ): Promise<DynamicLayoutRendererModalResult | undefined> {
    const modalData: DynamicLayoutRendererModalData = {
      ...data,
      enableEditMode: false,
      defaultView: 'view',
      showAllTab: false,
      title: options?.title || 'DYNAMIC_LAYOUT_RENDERER_MODAL.TITLES.VIEW_LAYOUT',
      width: options?.width,
      height: options?.height,
      disableClose: options?.disableClose
    };

    return this.open(modalData);
  }

  /**
   * Mở modal trong Form Edit mode
   * 
   * @param data Dữ liệu layout renderer với form values
   * @param options Tùy chọn modal (title, size, etc.)
   * @returns Promise<DynamicLayoutRendererModalResult | undefined>
   */
  async openEditMode(
    data: Omit<DynamicLayoutRendererModalData, 'enableEditMode' | 'title'>,
    options?: {
      title?: string;
      width?: string;
      height?: string;
      disableClose?: boolean;
    }
  ): Promise<DynamicLayoutRendererModalResult | undefined> {
    const modalData: DynamicLayoutRendererModalData = {
      ...data,
      enableEditMode: true,
      defaultView: 'form',
      showAllTab: true,
      title: options?.title || 'DYNAMIC_LAYOUT_RENDERER_MODAL.TITLES.EDIT_FORM',
      width: options?.width,
      height: options?.height,
      disableClose: options?.disableClose
    };

    return this.open(modalData);
  }

  /**
   * Mở modal với cấu hình tùy chỉnh hoàn toàn
   * 
   * @param data Dữ liệu đầy đủ cho modal
   * @param forceMode Force sử dụng dialog hoặc bottom-sheet
   * @returns Promise<DynamicLayoutRendererModalResult | undefined>
   */
  async openCustom(
    data: DynamicLayoutRendererModalData,
    forceMode?: 'dialog' | 'bottom-sheet'
  ): Promise<DynamicLayoutRendererModalResult | undefined> {
    try {
      const modalConfig = {
        data,
        width: data.width || '90vw',
        maxWidth: '1200px',
        minWidth: '600px',
        height: data.height || 'auto',
        maxHeight: '90vh',
        disableClose: data.disableClose || false,
        hasBackdrop: true,
        enableClose: data.enableClose !== false,
        actions: {
          useDefault: false
        }
      };

      const result = await this.responsiveModalService.open<
        DynamicLayoutRendererModalData,
        DynamicLayoutRendererModalResult,
        DynamicLayoutRendererModalComponent
      >(DynamicLayoutRendererModalComponent, modalConfig, forceMode);

      return result;

    } catch (error) {
      console.error('Error opening custom modal:', error);
      return undefined;
    }
  }
}
