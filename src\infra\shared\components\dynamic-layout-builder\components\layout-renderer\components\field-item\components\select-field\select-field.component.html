<!-- Select Field Container -->
@if (shouldShowField()) {
  <div class="select-field-container" [class.read-only]="isReadOnly()">
    
    <!-- Field Label -->
    <div class="field-label-container">
      <label class="field-label">
        <!-- Field icon trước label -->
        <mat-icon class="field-type-icon me-2" [style.color]="getFieldIconColor()">{{ getFieldIcon() }}</mat-icon>
        {{ config.field.label }}
        @if (isFieldRequired()) {
          <span class="required-asterisk">*</span>
        }
      </label>
      
      <!-- Read-only icon for form mode with read permission -->
      @if (isReadOnly()) {
        <mat-icon 
          class="read-only-icon ms-2"
          [matTooltip]="getReadOnlyTooltip() | translate"
          matTooltipPosition="above">
          lock
        </mat-icon>
      }
    </div>

    <!-- Field Value/Input -->
    <div class="field-value-container">
      
      <!-- VIEW MODE: Display placeholder data -->
      @if (config.currentViewMode === 'view') {
        <div class="field-view-value">
          <!-- Single select display -->
          @if (!isMultiSelect()) {
            <span class="placeholder-value">{{ placeholderValue() | translate }}</span>
          }

          <!-- Multi-select display with chips -->
          @else {
            <div class="chips-container">
              @for (value of placeholderValues(); track value) {
                <mat-chip class="placeholder-chip">
                  {{ value | translate }}
                </mat-chip>
              }
            </div>
          }
        </div>
      }
      
      <!-- FORM MODE: Display select control -->
      @else {
        <mat-form-field appearance="outline" class="w-100">
          <!-- Select element -->
          <mat-select
            [placeholder]="getPlaceholder() | translate"
            [formControl]="formControl()"
            [multiple]="isMultiSelect()"
            (selectionChange)="onSelectionChange($event)">
            
            <!-- Options -->
            @for (option of fieldOptions(); track option.value) {
              <mat-option [value]="option.value">
                {{ option.label | translate }}
              </mat-option>
            }
          </mat-select>
          
        </mat-form-field>

        <!-- Selected chips display for multi-select in form mode -->
        @if (isMultiSelect() && formControl().value && formControl().value.length > 0) {
          <div class="selected-chips-container">
            @for (selectedValue of formControl().value; track selectedValue) {
              <mat-chip 
                class="selected-chip"
                [removable]="!isReadOnly()"
                (removed)="removeChip(selectedValue)">
                {{ getOptionLabel(selectedValue) | translate }}
                @if (!isReadOnly()) {
                  <mat-icon matChipRemove>cancel</mat-icon>
                }
              </mat-chip>
            }
          </div>
        }
      }

    <!-- Field-Level Validation Errors -->
    @if (shouldShowValidationErrors()) {
      <div class="field-validation-errors" aria-live="polite">
        @for (error of getValidationErrors(); track error) {
          <div class="validation-error-item">
            <span class="error-message">{{ error | translate }}</span>
          </div>
        }
      </div>
    }
    </div>

    <!-- Field Tooltip/Description -->
    @if (config.field.tooltip) {
      <div class="field-tooltip">
        <small class="text-muted">{{ config.field.tooltip }}</small>
      </div>
    }

    <!-- Field Hints -->
    @if (config.currentViewMode === 'form' && isMultiSelect()) {
      <div class="field-hints">
        <small class="text-muted">
          {{ 'DYNAMIC_LAYOUT_RENDERER.FIELD_HINTS.MULTI_SELECT' | translate }}
        </small>
      </div>
    }
  </div>
}
