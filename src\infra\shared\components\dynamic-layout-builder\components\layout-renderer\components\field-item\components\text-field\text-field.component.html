<!-- Text Field Container -->
@if (shouldShowField()) {
  <div class="text-field-container" [class.read-only]="isReadOnly()">
    
    <!-- Field Label -->
    <div class="field-label-container">
      <label class="field-label">
        <!-- Field icon trước label -->
        <mat-icon class="field-type-icon me-2" [style.color]="getFieldIconColor()">{{ getFieldIcon() }}</mat-icon>
        {{ config.field.label }}
        @if (isFieldRequired()) {
          <span class="required-asterisk">*</span>
        }
      </label>
      
      <!-- Read-only icon for form mode with read permission -->
      @if (isReadOnly()) {
        <mat-icon 
          class="read-only-icon ms-2"
          [matTooltip]="getReadOnlyTooltip() | translate"
          matTooltipPosition="above">
          lock
        </mat-icon>
      }
    </div>

    <!-- Field Value/Input -->
    <div class="field-value-container">
      
      <!-- VIEW MODE: Display placeholder data -->
      @if (config.currentViewMode === 'view') {
        <div class="field-view-value">
          <span class="placeholder-value">{{ placeholderValue() }}</span>
        </div>
      }
      
      <!-- FORM MODE: Display input control -->
      @else {
        <mat-form-field appearance="outline" class="w-100">
          <!-- Input element -->
          <input
            matInput
            [type]="getInputType()"
            [placeholder]="getPlaceholder() | translate"
            [formControl]="formControl()"
            [readonly]="isReadOnly()"
            [class.read-only-cursor]="isReadOnly()">
          
        </mat-form-field>
      }
    </div>

    <!-- Field-Level Validation Errors -->
    @if (shouldShowValidationErrors()) {
      <div class="field-validation-errors" aria-live="polite">
        @for (error of getValidationErrors(); track error) {
          <div class="validation-error-item">
            <mat-icon class="error-icon">error</mat-icon>
            <span class="error-message">{{ error | translate }}</span>
          </div>
        }
      </div>
    }

    <!-- Field Tooltip/Description -->
    @if (config.field.tooltip) {
      <div class="field-tooltip">
        <small class="text-muted">{{ config.field.tooltip }}</small>
      </div>
    }
  </div>
}
