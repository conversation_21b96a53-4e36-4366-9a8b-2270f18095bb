<!-- Checkbox Field Container -->
@if (isVisible()) {
  <div class="checkbox-field-container" [class.read-only]="isReadOnlyState()">
    
    <!-- Field Label -->
    <div class="field-label-container">
      <label class="field-label">
        <!-- Field icon trước label -->
        <mat-icon class="field-type-icon me-2" [style.color]="getFieldIconColor()">{{ getFieldIcon() }}</mat-icon>
        {{ config.field.label }}
        @if (isFieldRequired()) {
          <span class="required-asterisk">*</span>
        }
      </label>
      
      @if (isReadOnlyState()) {
        <mat-icon
          class="read-only-icon ms-2"
          [matTooltip]="'DYNAMIC_LAYOUT_RENDERER.FIELD_MESSAGES.READ_ONLY_TOOLTIP' | translate"
          matTooltipPosition="above">
          lock
        </mat-icon>
      }
    </div>

    <!-- Field Value/Input -->
    <div class="field-value-container">
      
      <!-- VIEW MODE: Display placeholder data -->
      @if (config.currentViewMode === 'view') {
        <div class="field-view-value">
          <mat-icon class="checkbox-icon me-2" [class.checked]="placeholderValue()">
            {{ placeholderValue() ? 'check_box' : 'check_box_outline_blank' }}
          </mat-icon>
          <span class="checkbox-label">
            {{ (config.field.placeholder || 'DYNAMIC_LAYOUT_RENDERER.FIELD_PLACEHOLDERS.CHECKBOX') | translate }}
          </span>
          <span class="checkbox-status ms-2" [class.checked]="placeholderValue()">
            {{ placeholderValue() }}
          </span>
        </div>
      }
      
      <!-- FORM MODE: Display checkbox control -->
      @else {
        <div class="checkbox-form-container">
          <mat-checkbox
            [formControl]="formControl()"
            class="field-checkbox">
            {{ (config.field.placeholder || 'DYNAMIC_LAYOUT_RENDERER.FIELD_PLACEHOLDERS.CHECKBOX') | translate }}
          </mat-checkbox>
        
        </div>
      }

    <!-- Field-Level Validation Errors -->
    @if (shouldShowValidationErrors()) {
      <div class="field-validation-errors" aria-live="polite">
        @for (error of getValidationErrors(); track error) {
          <div class="validation-error-item">
            <mat-icon class="error-icon">error</mat-icon>
            <span class="error-message">{{ error | translate }}</span>
          </div>
        }
      </div>
    }
    </div>

    @if (config.field.tooltip) {
      <div class="field-tooltip">
        <small class="text-muted">{{ config.field.tooltip }}</small>
      </div>
    }
  </div>
}
