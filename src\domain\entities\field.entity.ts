
export type FieldPermission =  'read_write' | 'read' | 'none';
/**
 * Interface cho profile với quyền truy cập field
 */
export interface FieldPermissionProfile {
  _id: string;
  name: string;
  permission: FieldPermission
}

/**
 * Interface định nghĩa các loại field type có thể sử dụng trong hệ thống
 * Sử dụng union type để đảm bảo type safety
 */
export type TextFieldTypes = 'text' | 'email' | 'phone' | 'url' | 'search';
export type NumberFieldTypes = 'number' | 'decimal' | 'currency' | 'percent';
export type SelectFieldTypes = 'picklist' | 'multi-picklist' | 'select' | 'radio';
export type DateFieldTypes = 'date' | 'datetime';
export type FileFieldTypes = 'file' | 'image';
export type FieldType =
  | TextFieldTypes
  | NumberFieldTypes
  | SelectFieldTypes
  | DateFieldTypes
  | FileFieldTypes
  | 'textarea'
  | 'checkbox'
  | 'search'
  | 'user'
  ;

/**
 * Các field type khi render sẽ nhóm lại thành 1 số types, ví dụ 'text', 'email', 'phone', 'url' => text
 */
export type FieldComponentType = 'text' | 'number' | 'select' | 'date' | 'textarea' | 'checkbox' | 'file' | 'user' | 'unknown' ;

/**
 * Type cho field value - có thể là string, number, boolean, array, hoặc null
 */

export type FieldValue = string | number | boolean | Date | string[] | number[] | null | undefined;


/**
 * Interface cho field option object
 * Đã xóa _id để đơn giản hóa cấu trúc dữ liệu
 */
export interface FieldOption {
  label: string;
  value: string | number | boolean;
}

export interface BaseField {
  /**
   * mongo _id khi truyền từ server xuống
   * temp-randomStr khi tạo field mới
   */
  _id?: string;
  label: string;
  tooltip?: string;

  // Properties từ LayoutField - cần thiết cho Dynamic Layout Builder
  /**
   * Loại field - sử dụng FieldType để đảm bảo type safety
   */
  type: FieldType;
  order?: number;
  description?: string; // Description for field type selector
  placeholder?: string; // Placeholder text for form fields

  value?: FieldValue;
  defaultValue?: FieldValue;
  permissionProfiles: FieldPermissionProfile[];
}

export type Field =
  | TextField
  | EmailField
  | PhoneField
  | UrlField
  | TextareaField
  | NumberField
  | DecimalField
  | CurrencyField
  | PercentField
  | DateField
  | DateTimeField
  | PicklistField
  | MultiPicklistField
  | SearchField
  | UserField
  | UploadFileField
  | UploadImageField
  | CheckboxField;

export interface TextField extends BaseField {
  type: 'text';
  value?: string;
  constraints?: TextFieldConstraints;
}

export interface EmailField extends BaseField {
  type: 'email';
  value?: string;
  constraints?: TextFieldConstraints;
}

export interface PhoneField extends BaseField {
  type: 'phone';
  value?: string;
  constraints?: TextFieldConstraints;
}

export interface UrlField extends BaseField {
  type: 'url';
  value?: string;
  constraints?: TextFieldConstraints;
}

export interface TextareaField extends BaseField {
  type: 'textarea';
  value?: string;
  constraints?: TextareaFieldConstraints;
}

export interface NumberField extends BaseField {
  type: 'number';
  value?: number;
  constraints?: NumberFieldConstraints;
}

export interface DecimalField extends BaseField {
  type: 'decimal';
  value?: number;
  constraints?: DecimalFieldConstraints;
}

export interface CurrencyField extends BaseField {
  type: 'currency';
  value?: string;
  constraints?: CurrencyFieldConstraints;
}

export interface PercentField extends BaseField {
  type: 'percent';
  value?: number;
  constraints?: NumberFieldConstraints;
}

export interface DateField extends BaseField {
  type: 'date';
  value?: Date;
  constraints?: DateFieldConstraints;
}

export interface DateTimeField extends BaseField {
  type: 'datetime';
  value?: Date;
  constraints?: DateFieldConstraints;
}


export interface PicklistField extends BaseField {
  type: 'picklist';
  value?: string;
  constraints: PicklistFieldConstraints;
}

export interface MultiPicklistField extends BaseField {
  type: 'multi-picklist';
  value?: string[];
  constraints: PicklistFieldConstraints;
}

export interface SearchField extends BaseField {
  type: 'search';
  value?: string;
  constraints?: SearchFieldConstraints;
}

export interface UserField extends BaseField {
  type: 'user';
  value?: string;
  constraints?: UserFieldConstraints;
}

export interface UploadFileField extends BaseField {
  type: 'file';
  value?: string;
  constraints?: FileFieldConstraints;
}

export interface UploadImageField extends BaseField {
  type: 'image';
  value?: string;
  constraints?: FileFieldConstraints;
}

export interface CheckboxField extends BaseField {
  type: 'checkbox';
  value?: boolean;
  constraints?: CommonFieldConstraints;
}




/**
 * Type cho field constraints - định nghĩa các ràng buộc cho field
 */
export interface CommonFieldConstraints {
  // Common constraints
  isRequired?: boolean;
  isReadonly?: boolean;
  isDisabled?: boolean;
  isPublic?: boolean;
} 
export interface TextFieldConstraints extends CommonFieldConstraints {
  maxLength?: number;
  minLength?: number;
  pattern?: string;
  unique?: boolean;
  maxDigits?: number; // For phone fields
}
export interface TextareaFieldConstraints extends CommonFieldConstraints {
  maxLength?: number;
  minLength?: number;
  textType: 'small' | 'large' | 'rich';
}
export interface NumberFieldConstraints extends CommonFieldConstraints {
  maxDigits?: number;
  min?: number;
  max?: number;
  step?: number;
}
export interface DecimalFieldConstraints extends NumberFieldConstraints {
  decimalPlaces?: number;
  useNumberSeparator?: boolean;
}
export interface CurrencyFieldConstraints extends NumberFieldConstraints {
  decimalPlaces?: number;
  rounding?: 'normal' | 'off' | 'up' | 'down';
}
export interface PicklistFieldConstraints  extends CommonFieldConstraints {
  picklistOptions: FieldOption[],
  sortOrder?: 'input' | 'alphabetical';
}
export interface DateFieldConstraints  extends CommonFieldConstraints {
  // Date field constraints
  minDate?: Date;
  maxDate?: Date;
}
export interface FileFieldConstraints  extends CommonFieldConstraints {
  // File upload constraints
  allowedFileTypes?: string[];
  allowedTypes?: string[]; // Alias for allowedFileTypes
  maxFileSize?: number; // in bytes
  maxSize?: number; // Alias for maxFileSize
  allowMultipleFiles?: boolean;
  maxFiles?: number;
}
export interface UserFieldConstraints extends CommonFieldConstraints {
  userType?: 'single' | 'multiple';
}
export interface SearchFieldConstraints extends CommonFieldConstraints {
  searchModule?: 'sales_quotes' | 'contacts' | 'transactions';
}

export type FieldConstraints = TextFieldConstraints & TextareaFieldConstraints & NumberFieldConstraints & DecimalFieldConstraints & CurrencyFieldConstraints & DateFieldConstraints & PicklistFieldConstraints & FileFieldConstraints & UserFieldConstraints & SearchFieldConstraints;