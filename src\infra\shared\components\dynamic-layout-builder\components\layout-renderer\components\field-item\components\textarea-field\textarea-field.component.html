<!-- Textarea Field Container -->
@if (isVisible()) {
  <div class="textarea-field-container" [class.read-only]="isReadOnly()">
    
    <!-- Field Label -->
    <div class="field-label-container">
      <label class="field-label">
        <!-- Field icon trước label -->
        <mat-icon class="field-type-icon me-2" [style.color]="getFieldIconColor()">{{ getFieldIcon() }}</mat-icon>
        {{ config.field.label }}
        @if (isFieldRequired()) {
          <span class="required-asterisk">*</span>
        }
      </label>
      
      @if (isReadOnlyState()) {
        <mat-icon
          class="read-only-icon ms-2"
          [matTooltip]="'DYNAMIC_LAYOUT_RENDERER.FIELD_MESSAGES.READ_ONLY_TOOLTIP' | translate"
          matTooltipPosition="above">
          lock
        </mat-icon>
      }
    </div>

    <!-- Field Value/Input -->
    <div class="field-value-container">
      
      <!-- VIEW MODE: Display placeholder data -->
      @if (config.currentViewMode === 'view') {
        <div class="field-view-value">
          <div class="placeholder-value textarea-content">{{ placeholderValue() }}</div>
        </div>
      }
      
      <!-- FORM MODE: Display textarea control -->
      @else {
        <mat-form-field appearance="outline" class="w-100">
          <textarea
            matInput
            [placeholder]="(config.field.placeholder || 'DYNAMIC_LAYOUT_RENDERER.FIELD_PLACEHOLDERS.TEXTAREA') | translate"
            [formControl]="formControl()"
            [readonly]="isReadOnlyState()"
            [class.read-only-cursor]="isReadOnlyState()"
            rows="4"
            cdkTextareaAutosize
            cdkAutosizeMinRows="3"
            cdkAutosizeMaxRows="8">
          </textarea>
          
          <!-- Character count -->
          <mat-hint align="end">
            {{ getCharacterCount() }}/{{ getMaxLength() }}
          </mat-hint>
        </mat-form-field>
      }

    <!-- Field-Level Validation Errors -->
    @if (shouldShowValidationErrors()) {
      <div class="field-validation-errors" aria-live="polite">
        @for (error of getValidationErrors(); track error) {
          <div class="validation-error-item">
            <mat-icon class="error-icon">error</mat-icon>
            <span class="error-message">{{ error | translate }}</span>
          </div>
        }
      </div>
    }
    </div>

    @if (config.field.tooltip) {
      <div class="field-tooltip">
        <small class="text-muted">{{ config.field.tooltip }}</small>
      </div>
    }
  </div>
}
