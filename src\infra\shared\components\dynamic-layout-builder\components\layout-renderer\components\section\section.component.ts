import { Component, Input, Output, EventEmitter, ChangeDetectionStrategy, OnInit, signal } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatCardModule } from '@angular/material/card';
import { MatIconModule } from '@angular/material/icon';
import { TranslateModule } from '@ngx-translate/core';

import { SectionConfig, FieldItemConfig } from '../../../../models/dynamic-layout-renderer.model';
import { FieldItemComponent } from '../field-item/field-item.component';
import { Field, FieldValue } from '@domain/entities/field.entity';

/**
 * Component render section với dynamic CSS-based column layout
 *
 * Features:
 * - Render section title
 * - Dynamic CSS-based column layout (1 hoặc 2 cột)
 * - Render danh sách fields sử dụng FieldItemComponent
 * - Responsive behavior: tự động chuyển về single column trên mobile thông qua CSS
 * - Filter fields theo permission (không hiển thị fields có permission 'none')
 * - Sử dụng CSS Flexbox thay vì JavaScript để chia cột
 */
@Component({
  selector: 'app-section',
  standalone: true,
  imports: [
    CommonModule,
    MatCardModule,
    MatIconModule,
    TranslateModule,
    FieldItemComponent
  ],
  templateUrl: './section.component.html',
  styleUrls: ['./section.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class SectionComponent implements OnInit {

  /**
   * Configuration object chứa tất cả dữ liệu cần thiết
   * Tuân thủ yêu cầu: component chỉ nhận 1 input object
   */
  @Input({ required: true }) config!: SectionConfig;

  /**
   * Event emitter cho field value changes
   * Propagate events từ field components lên main component
   */
  @Output() valueChange = new EventEmitter<{ fieldId: string; value: FieldValue }>();

  // Signals để quản lý state - đã loại bỏ leftColumnFields và rightColumnFields
  visibleFields = signal<Field[]>([]);
  isDoubleColumn = signal<boolean>(false);

  
  ngOnInit(): void {
    this.initializeSection();
  }

  /**
   * Khởi tạo section dựa trên config
   * Đã loại bỏ logic chia cột thủ công - CSS sẽ xử lý layout
   */
  private initializeSection(): void {
    // Filter fields theo permission - không hiển thị fields có permission 'none'
    const filteredFields = this.config.section.fields.filter(field => {
      const fieldPermission = this.getFieldPermission(field);
      return fieldPermission !== 'none';
    });

    this.visibleFields.set(filteredFields);

    // Xác định layout column - chỉ để CSS biết cách render
    this.isDoubleColumn.set(
      this.config.section.settings.sectionLayoutColumn === 'double-column'
    );

    // Đã loại bỏ logic chia fields thành leftColumnFields và rightColumnFields
    // CSS Flexbox sẽ tự động xử lý việc chia cột
  }

  /**
   * Lấy permission của field dựa trên current permission profile
   * @param field Field cần kiểm tra permission
   * @returns Permission level của field
   */
  private getFieldPermission(field: Field): 'read_write' | 'read' | 'none' {
    // Tìm permission của field cho current profile
    const fieldPermission = field.permissionProfiles?.find(
      p => p._id === this.config.currentPermissionProfile._id
    );

    // Nếu không tìm thấy, sử dụng permission mặc định của profile
    return fieldPermission?.permission || this.config.currentPermissionProfile.permission;
  }

  /**
   * Tạo FieldItemConfig cho một field
   * @param field Field cần tạo config
   * @returns FieldItemConfig object
   */
  createFieldItemConfig(field: Field): FieldItemConfig {
    if(this.config.config.formValues?.[field._id!]) {
      field.value = this.config.config.formValues[field._id!] as any;
    }
    return {
      field: field,
      currentViewMode: this.config.currentViewMode,
      currentPermission: this.getFieldPermission(field),
      formDataService: this.config.formDataService // Truyền FormDataManagementService xuống field components
    };
  }

  /**
   * Kiểm tra xem section có fields để hiển thị hay không
   */
  hasVisibleFields(): boolean {
    return this.visibleFields().length > 0;
  }

  /**
   * Kiểm tra xem có nên hiển thị section title hay không
   */
  shouldShowTitle(): boolean {
    return !!this.config.section.title && this.config.section.title.trim().length > 0;
  }

  /**
   * Handle field value change events từ field components
   * Propagate event lên main component
   * @param event Event object chứa fieldId và value
   */
  onFieldValueChange(event: { fieldId: string; value: FieldValue }): void {
    // Propagate event lên main component
    this.valueChange.emit(event);
  }
}
